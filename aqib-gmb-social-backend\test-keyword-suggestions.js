const axios = require("axios");

// Test the keyword suggestions API
async function testKeywordSuggestions() {
  try {
    console.log("Testing keyword suggestions API...");

    // You'll need to replace this with a valid JWT token from your application
    const token = "YOUR_JWT_TOKEN_HERE";
    
    const testData = {
      businessName: "Sri Eye Care Speciality Eye Hospital",
      businessCategory: "Healthcare",
      location: "Bangalore, India",
      businessType: "Eye Hospital"
    };

    const response = await axios.post(
      "http://localhost:3001/api/v1/local-falcon/keyword-suggestions",
      testData,
      {
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json"
        }
      }
    );

    console.log("✅ API Response:", JSON.stringify(response.data, null, 2));
    
    if (response.data.success && response.data.data.keywords) {
      console.log("\n📝 Generated Keywords:");
      response.data.data.keywords.forEach((keyword, index) => {
        console.log(`${index + 1}. ${keyword}`);
      });
    }

  } catch (error) {
    console.error("❌ Error testing API:", error.response?.data || error.message);
  }
}

// Run the test
testKeywordSuggestions();
