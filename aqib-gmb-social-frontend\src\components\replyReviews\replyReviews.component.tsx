import React, {
  FunctionComponent,
  useContext,
  useEffect,
  useState,
} from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import Grid from "@mui/material/Grid";
import SmartToyOutlinedIcon from "@mui/icons-material/SmartToyOutlined";
import { IReviewsResponse } from "../../interfaces/response/IReviewsListResponseModel";
import UserAvatarWithName from "../userAvatarWIthName/userAvatarWIthName.component";
import RatingsStar from "../ratingsStar/ratingsStar.component";
import ApplicationHelperService from "../../services/ApplicationHelperService";
import moment from "moment";
import ReviewService from "../../services/review/review.service";
import { LoadingContext } from "../../context/loading.context";
import { IAiReviewResponseModel } from "../../interfaces/response/IAiReviewResponseModel";
import ReplyTwoToneIcon from "@mui/icons-material/ReplyTwoTone";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import { useDispatch, useSelector } from "react-redux";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import { MessageConstants } from "../../constants/message.constant";
import { display } from "html2canvas/dist/types/css/property-descriptors/display";
import { AutoAwesome as AutoAwesomeIcon } from "@mui/icons-material";

const ReplyReviewsComponent = (props: {
  review: IReviewsResponse;
  closeDrawer: () => null | void | undefined;
}) => {
  const dispatch = useDispatch();
  const { setLoading } = useContext(LoadingContext);
  const _applicationHelperService = new ApplicationHelperService({});
  const _reviewService = new ReviewService(dispatch);
  const [reviewReply, setReviewReply] = useState<string>("");
  const { setToastConfig, setOpen } = useContext(ToastContext);
  const { userInfo } = useSelector((state: any) => state.authReducer);

  const reply = async () => {
    try {
      setLoading(true);
      const headerData = {
        "x-gmb-location-id": props.review.locationId,
        "x-gmb-account-id": props.review.gmbAccountId,
        "x-gmb-review-id": props.review.reviewId,
      };
      var response = await _reviewService.replyToReview(headerData, {
        comment: reviewReply,
        userId: userInfo.id,
      });
    } catch (error: any) {
      if (error?.response?.data?.error) {
        setToastConfig(ToastSeverity.Error, error?.response?.data?.error, true);
      } else {
        setToastConfig(
          ToastSeverity.Error,
          MessageConstants.ApiErrorStandardMessage,
          true
        );
      }
    } finally {
      setLoading(false);
    }
  };

  const getReplyFromAI = async () => {
    if (props.review.review) {
      try {
        const aIReviewResponse: IAiReviewResponseModel =
          await _reviewService.getReviewReplyFromAI(
            props.review.review,
            _applicationHelperService.getRatingNumberFromText(
              props.review.starRating
            )
          );

        if (aIReviewResponse.message) {
          setReviewReply(aIReviewResponse.message);
        }
      } catch (error) {}
    }
  };

  return (
    <Box
      className="commonModal rolePermissionsModal"
      sx={{ display: "flex", flexDirection: "column", height: "100%" }}
    >
      <Typography
        id="modal-modal-title"
        variant="h6"
        component="h2"
        className="modal-modal-title"
      >
        Reply To Review
      </Typography>

      <Box
        id="modal-modal-description"
        className="modal-modal-description"
        sx={{
          flex: 1,
          overflowY: "auto",
          paddingBottom: 0,
        }}
      >
        <Box>
          <Box>
            <Typography>
              {_applicationHelperService.getFormatedDate(
                moment(props.review.createTime, "YYYY-MM-DD").toDate(),
                "MMM DD, YYYY"
              )}
            </Typography>
            <RatingsStar starRating={props.review.starRating} />
            <Box>
              <UserAvatarWithName fullname={props.review.reviewerName} />
            </Box>
            <Box>
              <Box>
                <Typography variant="subtitle2">{"Review"}</Typography>
              </Box>
              <Box>
                <Typography style={{ textAlign: "justify" }}>
                  {props.review.review}
                </Typography>
              </Box>
            </Box>
            {props.review.reviewReplyComment && (
              <Box sx={{ marginTop: 3 }}>
                <Box>
                  <Typography
                    style={{ display: "flex", justifyContent: "flex-end" }}
                    variant="subtitle2"
                  >
                    {"Reply"}
                  </Typography>
                </Box>
                <Box>
                  <Typography style={{ textAlign: "justify" }} variant="body1">
                    {props.review.reviewReplyComment}
                  </Typography>
                </Box>
              </Box>
            )}
          </Box>
        </Box>
        <Box sx={{ marginTop: 3, display: "flex", justifyContent: "flex-end" }}>
          <Button
            variant="contained"
            startIcon={<AutoAwesomeIcon />}
            onClick={() => getReplyFromAI()}
            sx={{ textTransform: "none", width: { xs: "100%", md: "50%" } }}
            className="primaryFillBtn"
          >
            AI Smart Reply
          </Button>
        </Box>
        <Box sx={{ marginTop: 3 }}>
          <TextField
            value={reviewReply}
            label="Reply"
            multiline
            rows={4} // Number of visible rows
            variant="outlined"
            fullWidth
            placeholder="Type your message here..."
            onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
              setReviewReply(event.target.value)
            }
          />
        </Box>
      </Box>

      <Box className="commonFooter">
        <Button
          onClick={props.closeDrawer}
          variant="outlined"
          className="closeFillBtn"
          startIcon={<CancelOutlinedIcon />}
        >
          Cancel
        </Button>
        <Button
          onClick={() => reply()}
          variant="contained"
          className="primaryFillBtn"
          startIcon={<ReplyTwoToneIcon />}
        >
          Reply
        </Button>
      </Box>
    </Box>
  );
};

export default ReplyReviewsComponent;
