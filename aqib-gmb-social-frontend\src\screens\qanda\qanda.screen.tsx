import { FunctionComponent, useContext, useEffect, useState } from "react";
import PageProps from "../../models/PageProps.interface";

//Widgets
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import InputLabel from "@mui/material/InputLabel";

import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import Card from "@mui/material/Card";
import {
  CardContent,
  Divider,
  Drawer,
  Grid2,
  List,
  ListItem,
  SelectChangeEvent,
  FormHelperText,
} from "@mui/material";
import LeftMenuComponent from "../../components/leftMenu/leftMenu.component";
import "../qanda/qanda.screen.style.css";
import { Formik } from "formik";
import {
  IBusinessGroup,
  IBusinessGroupsResponseModel,
} from "../../interfaces/response/IBusinessGroupsResponseModel";
import * as yup from "yup";
import { DEFAULT_PAGINATION } from "../../constants/dbConstant.constant";
import { IPaginationModel } from "../../interfaces/IPaginationModel";
import {
  ILocation,
  ILocationsListResponseModel,
} from "../../interfaces/response/ILocationsListResponseModel";
import { useDispatch, useSelector } from "react-redux";
import { LoadingContext } from "../../context/loading.context";
import BusinessService from "../../services/business/business.service";
import LocationService from "../../services/location/location.service";
import {
  IBusiness,
  IBusinessListResponseModel,
} from "../../interfaces/response/IBusinessListResponseModel";
import { IReviewsListRequestModel } from "../../interfaces/request/IReviewsListRequestModel";
import ApplicationHelperService from "../../services/ApplicationHelperService";
import {
  IQuestionAnswer,
  IQuestionAnswersResponseModel,
} from "../../interfaces/response/IQuestionAnswersResponseModel";
import GenericDrawer from "../../components/genericDrawer/genericDrawer.component";
import QandAService from "../../services/qanda/qanda.service";
import SendOutlinedIcon from "@mui/icons-material/SendOutlined";
import ReplyQuestionAnswerComponent from "../../components/replyQuestionAnswer/replyQuestionAnswer.component";
import { getIn } from "formik";
import { ToastSeverity } from "../../constants/toastSeverity.constant";
import { MessageConstants } from "../../constants/message.constant";
import { ToastContext } from "../../context/toast.context";
import FilterListIcon from "@mui/icons-material/FilterList";

const QandA: FunctionComponent<PageProps> = ({ title }) => {
  const dispatch = useDispatch();
  const [openqanda, setOpenqanda] = useState(false);
  const { userInfo, rbAccess } = useSelector((state: any) => state.authReducer);
  const _businessService = new BusinessService(dispatch);
  const _locationService = new LocationService(dispatch);
  const _qandAService = new QandAService(dispatch);
  const _applicationHelperService = new ApplicationHelperService({});
  const { setLoading } = useContext(LoadingContext);
  const { setToastConfig, setOpen } = useContext(ToastContext);
  const [businessGroups, setBusinessGroups] = useState<IBusinessGroup[]>([]);
  const [businessGroupsOnBusiness, setBusinessGroupsOnBusiness] = useState<
    IBusinessGroup[]
  >([]);
  const [qandaList, setQandAList] = useState<IQuestionAnswer[]>([]);

  // const [locationListOnBusiness, setLocationListOnBusiness] = useState<
  //   ILocation[]
  // >([]);
  const [paginationModel, setPaginationModel] =
    useState<IPaginationModel>(DEFAULT_PAGINATION);
  const [locationList, setLocationList] = useState<ILocation[]>([]);
  const [businessList, setBusinessList] = useState<IBusiness[]>([]);
  const INITIAL_VALUES: IReviewsListRequestModel = {
    businessId: 0,
    businessGroupId: "0",
    locationId: "0",
    orderBy: "CREATETIME ASC",
    rating: "ALL",
    tags: "0",
    searchText: "",
  };

  const [initialValues, setInitialValues] =
    useState<IReviewsListRequestModel>(INITIAL_VALUES);

  const getBusiness = async () => {
    try {
      setLoading(true);
      let roles: IBusinessListResponseModel =
        await _businessService.getBusiness(userInfo.id);
      if (roles.list.length > 0) {
        setBusinessList(roles.list);
      }
    } catch (error) {}

    setLoading(false);
  };

  const getBusinessGroups = async () => {
    try {
      setLoading(true);
      let businessGroups: IBusinessGroupsResponseModel =
        await _businessService.getBusinessGroups(userInfo.id);
      if (businessGroups.data.length > 0) {
        setBusinessGroups(businessGroups.data);
      }
    } catch (error) {}

    setLoading(false);
  };

  const LocationSchema = yup.object().shape({
    businessId: yup
      .number()
      .typeError("Business ID must be a number")
      .moreThan(0, "Business must be selected") // Ensures value is greater than 0
      .required("Business is required"),
    businessGroupId: yup
      .string()
      .nonNullable()
      .required("Account is required")
      .test("len", `Account is required`, (val) => val != "0"),
    locationId: yup
      .string()
      .nonNullable()
      .required("Location is required")
      .test("len", `Location is required`, (val) => val != "0"),
  });

  useEffect(() => {
    getBusiness();
    getBusinessGroups();
    fetchLocationsPaginated();
  }, []);

  const fetchLocationsPaginated = async () => {
    try {
      setLoading(true);
      const locationListResponse: ILocationsListResponseModel =
        await _businessService.getLocations(userInfo.id);
      console.log("Location List: ", locationListResponse.list);
      setLocationList(locationListResponse.list);
    } catch (error) {}

    setLoading(false);
  };

  const fetchQuestionsAnswersListPaginated = async (locationId: string) => {
    try {
      setLoading(true);
      const reviewsResponse: IQuestionAnswersResponseModel =
        await _qandAService.getQA(userInfo.id, locationId);
      console.log("Question and Answers Response: ", reviewsResponse);
      setQandAList(reviewsResponse.list);
    } catch (error) {}

    setLoading(false);
  };

  const fetchQuestionsAnswers = async (values: IReviewsListRequestModel) => {
    try {
      const isValid = await LocationSchema.isValid(values);
      if (isValid) {
        fetchQuestionsAnswersListPaginated(values.locationId);
      }
    } catch (error: any) {}
  };

  const syncQandA = async (values: IReviewsListRequestModel) => {
    try {
      setLoading(true);
      const syncReviewsRequest = {
        "x-gmb-account-id": values.businessGroupId,
        "x-gmb-business-id": values.businessId,
        "x-gmb-location-id": values.locationId,
      };

      const reviewsResponse: IQuestionAnswersResponseModel =
        await _qandAService.refreshQA(syncReviewsRequest);

      if (reviewsResponse.success) {
        setToastConfig(ToastSeverity.Success, reviewsResponse.message, true);
        fetchQuestionsAnswers(values);
      } else {
        setToastConfig(ToastSeverity.Error, reviewsResponse.message, true);
      }
    } catch (error: any) {
      setLoading(false);
      if (error?.response?.data?.data?.error?.details) {
        setToastConfig(
          ToastSeverity.Error,
          error?.response?.data?.data?.error?.details[0].reason,
          true
        );
      } else {
        setToastConfig(
          ToastSeverity.Error,
          MessageConstants.ApiErrorStandardMessage,
          true
        );
      }
    }
  };

  const QuestionAnswersComponent = (props: {
    qandanswer: IQuestionAnswer;
    index: number;
  }) => {
    const [openReplyMessage, setOpenReplyMessage] = useState<boolean>(false);

    return (
      <Grid2 size={6} key={props.index}>
        <Card variant="outlined" className="commonCard reviewCard height100">
          <CardContent className="pad0">
            <Box>
              <h3 className="m-0">{props.qandanswer.question}</h3>
              <Box>
                <List className="">
                  <ListItem className="pad0">
                    <Typography className="textTruncate">
                      <b>Answer: </b>
                      {props.qandanswer.answer}.
                    </Typography>
                  </ListItem>
                </List>
                {Boolean(rbAccess && rbAccess.QandAReply) && (
                  <Box className="qandaReply">
                    <Button
                      className="tagBtn"
                      onClick={() => setOpenReplyMessage(true)}
                      variant="contained"
                      startIcon={<SendOutlinedIcon />}
                    >
                      Reply
                    </Button>
                  </Box>
                )}
              </Box>
            </Box>
          </CardContent>
        </Card>

        <GenericDrawer
          component={
            <ReplyQuestionAnswerComponent
              questionAnswer={props.qandanswer}
              closeDrawer={() => setOpenReplyMessage(!openReplyMessage)}
            />
          }
          isShow={openReplyMessage}
          callback={() => setOpenReplyMessage(!openReplyMessage)}
        />
      </Grid2>
    );
  };

  return (
    <div>
      <Box>
        <Box>
          <LeftMenuComponent>
            <Box className="bodyPart">
              <Formik
                enableReinitialize
                initialValues={{ ...initialValues }}
                validationSchema={LocationSchema}
                onSubmit={(values, { setSubmitting }) => {
                  fetchQuestionsAnswers(values);
                }}
              >
                {({
                  values,
                  errors,
                  touched,
                  handleChange,
                  handleBlur,
                  handleSubmit,
                  setFieldValue,
                  handleReset,
                  isSubmitting,
                  isValid,
                  /* and other goodies */
                }) => (
                  <form onSubmit={handleSubmit} onReset={handleReset}>
                    <Box className="commonTableHeader">
                      <h3 className="commonTitle pageTitle">
                        List of queries for Business
                      </h3>

                      {Boolean(rbAccess && rbAccess.ReviewsRefresh) && (
                        <Button
                          onClick={() => syncQandA(values)}
                          className={
                            values.locationId === "0"
                              ? "tableActionBtnDisabled"
                              : "tableActionBtn"
                          }
                          sx={{
                            minHeight: "50px", // Set the desired height
                            width: "100px",
                          }}
                          startIcon={
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 48 48"
                              width="24px"
                              height="24px"
                            >
                              <path
                                fill="#fbc02d"
                                d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12 s5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24s8.955,20,20,20 s20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
                              />
                              <path
                                fill="#e53935"
                                d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039 l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"
                              />
                              <path
                                fill="#4caf50"
                                d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36 c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"
                              />
                              <path
                                fill="#1565c0"
                                d="M43.611,20.083L43.595,20L42,20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571 c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"
                              />
                            </svg>
                          }
                          disabled={values.locationId === "0"}
                        >
                          Sync
                        </Button>
                      )}
                    </Box>
                    <Divider sx={{ mb: 2 }}></Divider>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6} lg={3}>
                        <FormControl
                          variant="filled"
                          fullWidth
                          error={Boolean(
                            getIn(errors, "businessId") &&
                              getIn(touched, "businessId")
                          )}
                        >
                          <InputLabel id="outlined-country-dropdown-label">
                            Business
                          </InputLabel>

                          <Select
                            fullWidth
                            id="businessId"
                            label="Business"
                            value={values.businessId.toString()}
                            onChange={(evt: SelectChangeEvent) => {
                              setBusinessGroupsOnBusiness(
                                businessGroups.filter(
                                  (x: IBusinessGroup) =>
                                    x.businessId === +evt.target.value
                                )
                              );
                              setFieldValue("businessId", +evt.target.value);
                              setFieldValue("businessGroupId", "0");
                              setFieldValue("locationId", "0");
                              setPaginationModel({ ...DEFAULT_PAGINATION });
                            }}
                            onBlur={handleBlur}
                            sx={{
                              backgroundColor: "var(--whiteColor)",
                              borderRadius: "5px",
                            }}
                          >
                            <MenuItem value={0}>Select</MenuItem>
                            {businessList &&
                              businessList.map((business: IBusiness) => (
                                <MenuItem
                                  key={business.id}
                                  value={business.id.toString()}
                                >
                                  {business.businessName}
                                </MenuItem>
                              ))}
                          </Select>
                          <FormHelperText>
                            {touched.businessId && errors.businessId
                              ? errors.businessId
                              : ""}
                          </FormHelperText>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} md={6} lg={3}>
                        <FormControl
                          variant="filled"
                          fullWidth
                          error={Boolean(
                            getIn(errors, "businessGroupId") &&
                              getIn(touched, "businessGroupId")
                          )}
                        >
                          <InputLabel id="outlined-country-dropdown-label">
                            Group
                          </InputLabel>

                          <Select
                            fullWidth
                            id="businessGroupId"
                            label="Group"
                            value={values.businessGroupId || ""} // Ensure default is empty string
                            onChange={(evt: SelectChangeEvent) => {
                              const selectedValue = evt.target.value;
                              setFieldValue("businessGroupId", selectedValue); // Ensure consistent type
                              setFieldValue("locationId", "0");
                              setPaginationModel({ ...DEFAULT_PAGINATION });
                            }}
                            onBlur={handleBlur}
                            sx={{
                              backgroundColor: "var(--whiteColor)",
                              borderRadius: "5px",
                            }}
                          >
                            <MenuItem value="0">Select</MenuItem>
                            {businessGroups
                              .filter(
                                (x: IBusinessGroup) =>
                                  x.businessId === values.businessId
                              )
                              .map((businessGroup: IBusinessGroup) => (
                                <MenuItem
                                  key={businessGroup.accountId}
                                  value={businessGroup.accountId}
                                >
                                  {businessGroup.accountName}
                                </MenuItem>
                              ))}
                          </Select>
                          <FormHelperText>
                            {touched.businessGroupId && errors.businessGroupId
                              ? errors.businessGroupId
                              : ""}
                          </FormHelperText>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} md={6} lg={3}>
                        <FormControl
                          variant="filled"
                          fullWidth
                          error={Boolean(
                            getIn(errors, "locationId") &&
                              getIn(touched, "locationId")
                          )}
                        >
                          <InputLabel id="outlined-country-dropdown-label">
                            Location
                          </InputLabel>

                          <Select
                            fullWidth
                            id="locationId"
                            label="Location"
                            value={values.locationId.toString()}
                            onChange={(evt: SelectChangeEvent) => {
                              setFieldValue("locationId", evt.target.value);
                              setPaginationModel({ ...DEFAULT_PAGINATION });
                            }}
                            onBlur={handleBlur}
                            sx={{
                              backgroundColor: "var(--whiteColor)",
                              borderRadius: "5px",
                            }}
                          >
                            <MenuItem value={"0"}>Select</MenuItem>
                            {locationList
                              .filter(
                                (x: ILocation) =>
                                  x.gmbAccountId === values.businessGroupId
                              )
                              .map((location: ILocation) => (
                                <MenuItem
                                  key={location.gmbLocationId}
                                  value={location.gmbLocationId}
                                >
                                  {location.gmbLocationName}
                                </MenuItem>
                              ))}
                          </Select>
                          <FormHelperText>
                            {touched.locationId && errors.locationId
                              ? errors.locationId
                              : ""}
                          </FormHelperText>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} md={6} lg={3}>
                        <Button
                          className="commonShapeBtn"
                          sx={{ py: 2, px: 5 }}
                          type="submit"
                          variant="contained"
                          startIcon={<FilterListIcon />}
                          fullWidth
                        >
                          Apply
                        </Button>
                      </Grid>
                    </Grid>
                  </form>
                )}
              </Formik>
              <Box>
                <Box>
                  {/* <Box className="commonTableHeader">
                    <h3>Q&A</h3>
                  </Box> */}
                  <Grid2 container spacing={2}>
                    {qandaList.length > 0 &&
                      qandaList.map(
                        (question: IQuestionAnswer, index: number) => (
                          <QuestionAnswersComponent
                            key={"q-and-a" + index}
                            qandanswer={question}
                            index={index}
                          />
                        )
                      )}
                  </Grid2>
                </Box>
              </Box>
            </Box>
          </LeftMenuComponent>
        </Box>
      </Box>
    </div>
  );
};

export default QandA;
