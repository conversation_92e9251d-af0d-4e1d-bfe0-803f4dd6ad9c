import React, { useState } from "react";
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Avatar,
  IconButton,
  Divider,
  CardMedia,
} from "@mui/material";
import {
  FavoriteBorder,
  ChatBubbleOutline,
  SendOutlined,
  <PERSON>markBorder,
  MoreH<PERSON>z,
  ArrowBackIos,
  ArrowForwardIos,
  PlayArrow,
} from "@mui/icons-material";
import {
  IInstagramCreatePost,
  IInstagramSelectedAccount,
} from "../../interfaces/request/IInstagramCreatePost";
import { IInstagramAccountData } from "../../interfaces/response/IInstagramCreatePostResponse";

interface InstagramPostPreviewProps {
  formData: IInstagramCreatePost;
  uploadedImages: any[];
  selectedAccount?: IInstagramAccountData | null;
  selectedAccounts?: IInstagramSelectedAccount[];
  enableMultiAccountSelection?: boolean;
  showDisconnectedPreview?: boolean;
}

const InstagramPostPreview: React.FC<InstagramPostPreviewProps> = ({
  formData,
  uploadedImages,
  selectedAccount,
  selectedAccounts = [],
  enableMultiAccountSelection = false,
  showDisconnectedPreview = false,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % uploadedImages.length);
  };

  const handlePrev = () => {
    setCurrentIndex(
      (prevIndex) =>
        (prevIndex - 1 + uploadedImages.length) % uploadedImages.length
    );
  };

  // Get preview account (first selected account or provided account)
  const previewAccount =
    enableMultiAccountSelection && selectedAccounts.length > 0
      ? selectedAccounts[0]
      : selectedAccount;

  // Show disconnected preview with placeholder data
  const disconnectedAccount = {
    accountName: "Your Instagram Account",
    profilePicture: "",
    username: "your_username",
  };

  const getImageUrl = (image: any) => {
    if ((image as any).s3Url) {
      return (image as any).s3Url;
    } else if (image instanceof File) {
      return URL.createObjectURL(image);
    }
    return "";
  };

  const isVideo = (image: any) => {
    return image?.type?.startsWith("video/") || formData.mediaType === "video";
  };

  // Replace placeholders in caption
  const getProcessedCaption = () => {
    let caption = formData.caption || "";
    const currentAccount = showDisconnectedPreview
      ? disconnectedAccount
      : previewAccount;
    if (currentAccount) {
      const accountName =
        currentAccount.accountName ||
        selectedAccount?.account_name ||
        "Account Name";
      caption = caption.replace(/{Account Name}/g, accountName);
    }
    return caption;
  };

  // Get current account for display
  const displayAccount = showDisconnectedPreview
    ? disconnectedAccount
    : previewAccount;

  return (
    <Box sx={{ maxWidth: "100%", mx: "auto" }}>
      {showDisconnectedPreview && (
        <Box
          sx={{
            mb: 2,
            p: 2,
            backgroundColor: "#f9fafb",
            borderRadius: 1,
            border: "1px solid #e0e0e0",
            textAlign: "center",
          }}
        >
          <Typography variant="body2" color="text.secondary">
            Connect your Instagram account to see post preview
          </Typography>
        </Box>
      )}

      <Card
        elevation={3}
        sx={{
          borderRadius: 3,
          overflow: "hidden",
          backgroundColor: "#fff",
          border: "1px solid #dbdbdb",
        }}
      >
        {/* Header */}
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            p: 2,
            borderBottom: "1px solid #efefef",
          }}
        >
          <Avatar
            src={
              showDisconnectedPreview
                ? (displayAccount as any)?.profilePicture || ""
                : (previewAccount as any)?.accountPictureUrl ||
                  selectedAccount?.account_picture_url ||
                  ""
            }
            sx={{ width: 32, height: 32, mr: 2 }}
          >
            {(showDisconnectedPreview
              ? (displayAccount as any)?.accountName || "A"
              : (previewAccount as any)?.accountName ||
                selectedAccount?.account_name ||
                "A"
            )
              .charAt(0)
              .toUpperCase()}
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography
              variant="subtitle2"
              sx={{ fontWeight: 600, fontSize: 14 }}
            >
              {showDisconnectedPreview
                ? (displayAccount as any)?.username || "your_username"
                : (previewAccount as any)?.accountUsername ||
                  selectedAccount?.account_username ||
                  "username"}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {showDisconnectedPreview
                ? (displayAccount as any)?.accountName ||
                  "Your Instagram Account"
                : (previewAccount as any)?.accountName ||
                  selectedAccount?.account_name ||
                  "Account Name"}
            </Typography>
          </Box>
          <IconButton size="small">
            <MoreHoriz />
          </IconButton>
        </Box>

        {/* Media Section */}
        {uploadedImages.length > 0 ? (
          <Box sx={{ position: "relative", aspectRatio: "1/1" }}>
            <CardMedia
              component={
                isVideo(uploadedImages[currentIndex]) ? "video" : "img"
              }
              image={getImageUrl(uploadedImages[currentIndex])}
              src={getImageUrl(uploadedImages[currentIndex])}
              sx={{
                width: "100%",
                height: "100%",
                objectFit: "cover",
              }}
              controls={isVideo(uploadedImages[currentIndex])}
            />

            {/* Video Play Icon Overlay */}
            {isVideo(uploadedImages[currentIndex]) && (
              <Box
                sx={{
                  position: "absolute",
                  top: "50%",
                  left: "50%",
                  transform: "translate(-50%, -50%)",
                  backgroundColor: "rgba(0,0,0,0.6)",
                  borderRadius: "50%",
                  p: 1,
                }}
              >
                <PlayArrow sx={{ color: "white", fontSize: 32 }} />
              </Box>
            )}

            {/* Navigation arrows for multiple images */}
            {uploadedImages.length > 1 && (
              <>
                <IconButton
                  onClick={handlePrev}
                  sx={{
                    position: "absolute",
                    left: 8,
                    top: "50%",
                    transform: "translateY(-50%)",
                    backgroundColor: "rgba(0,0,0,0.5)",
                    color: "white",
                    "&:hover": {
                      backgroundColor: "rgba(0,0,0,0.7)",
                    },
                  }}
                  size="small"
                >
                  <ArrowBackIos fontSize="small" />
                </IconButton>
                <IconButton
                  onClick={handleNext}
                  sx={{
                    position: "absolute",
                    right: 8,
                    top: "50%",
                    transform: "translateY(-50%)",
                    backgroundColor: "rgba(0,0,0,0.5)",
                    color: "white",
                    "&:hover": {
                      backgroundColor: "rgba(0,0,0,0.7)",
                    },
                  }}
                  size="small"
                >
                  <ArrowForwardIos fontSize="small" />
                </IconButton>

                {/* Dots indicator */}
                <Box
                  sx={{
                    position: "absolute",
                    bottom: 12,
                    left: "50%",
                    transform: "translateX(-50%)",
                    display: "flex",
                    gap: 0.5,
                  }}
                >
                  {uploadedImages.map((_, index) => (
                    <Box
                      key={index}
                      sx={{
                        width: 6,
                        height: 6,
                        borderRadius: "50%",
                        backgroundColor:
                          index === currentIndex
                            ? "white"
                            : "rgba(255,255,255,0.5)",
                      }}
                    />
                  ))}
                </Box>
              </>
            )}
          </Box>
        ) : (
          <Box
            sx={{
              aspectRatio: "1/1",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "#f5f5f5",
              color: "text.secondary",
            }}
          >
            <Typography variant="body2">No media selected</Typography>
          </Box>
        )}

        {/* Action Buttons */}
        <Box sx={{ p: 2 }}>
          <Box sx={{ display: "flex", justifyContent: "space-between", mb: 2 }}>
            <Box sx={{ display: "flex", gap: 2 }}>
              <IconButton size="small">
                <FavoriteBorder />
              </IconButton>
              <IconButton size="small">
                <ChatBubbleOutline />
              </IconButton>
              <IconButton size="small">
                <SendOutlined />
              </IconButton>
            </Box>
            <IconButton size="small">
              <BookmarkBorder />
            </IconButton>
          </Box>

          {/* Likes */}
          <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 1 }}>
            0 likes
          </Typography>

          {/* Caption */}
          {getProcessedCaption() && (
            <Box sx={{ mb: 1 }}>
              <Typography
                variant="body2"
                component="span"
                sx={{ fontWeight: 600 }}
              >
                {showDisconnectedPreview
                  ? (displayAccount as any)?.username || "your_username"
                  : (previewAccount as any)?.accountUsername ||
                    selectedAccount?.account_username ||
                    "username"}{" "}
              </Typography>
              <Typography variant="body2" component="span">
                {getProcessedCaption()}
              </Typography>
            </Box>
          )}

          {/* Timestamp */}
          <Typography variant="caption" color="text.secondary">
            {formData.scheduledPublishTime
              ? `Scheduled for ${new Date(
                  formData.scheduledPublishTime
                ).toLocaleString()}`
              : "Just now"}
          </Typography>
        </Box>
      </Card>

      {/* Multi-account indicator */}
      {enableMultiAccountSelection && selectedAccounts.length > 1 && (
        <Box
          sx={{
            mt: 2,
            p: 2,
            backgroundColor: "#e3f2fd",
            borderRadius: 1,
            border: "1px solid #bbdefb",
          }}
        >
          <Typography
            variant="body2"
            sx={{ color: "#1976d2", fontWeight: 600 }}
          >
            📄 Multi-Account Preview
          </Typography>
          <Typography variant="caption" sx={{ color: "#1565c0" }}>
            This preview shows how the post will look for "
            {selectedAccounts[0].accountName}". Similar posts will be created
            for all {selectedAccounts.length} selected accounts.
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default InstagramPostPreview;
