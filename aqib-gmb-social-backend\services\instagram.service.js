const axios = require("axios");
const logger = require("../utils/logger");
const pool = require("../config/db");

class InstagramService {
  constructor() {
    this.clientId = process.env.INSTAGRAM_CLIENT_ID;
    this.clientSecret = process.env.INSTAGRAM_CLIENT_SECRET;
    this.redirectUri = process.env.INSTAGRAM_REDIRECT_URI;
    this.apiVersion = process.env.INSTAGRAM_API_VERSION || "v20.0";
    this.baseURL = `https://graph.facebook.com/${this.apiVersion}`;

    // Debug logging for environment variables
    logger.info("Instagram Service Configuration", {
      hasClientId: !!this.clientId,
      hasClientSecret: !!this.clientSecret,
      hasRedirectUri: !!this.redirectUri,
      clientId: this.clientId
        ? `${this.clientId.substring(0, 8)}...`
        : "undefined",
      redirectUri: this.redirectUri,
      apiVersion: this.apiVersion,
    });
  }

  /**
   * Validate Instagram configuration
   * @returns {boolean} Configuration validity
   */
  validateConfig() {
    const requiredVars = [
      "INSTAGRAM_CLIENT_ID",
      "INSTAGRAM_CLIENT_SECRET",
      "INSTAGRAM_REDIRECT_URI",
    ];

    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        logger.error(`Missing required environment variable: ${varName}`);
        return false;
      }
    }

    return true;
  }

  /**
   * Generate Instagram OAuth URL
   * @param {number} userId - User ID
   * @returns {string} OAuth URL
   */
  generateAuthUrl(userId) {
    try {
      // Instagram Business API requires these Facebook permissions
      // to access Instagram Business accounts through Facebook Pages
      // Using only valid scopes as per current Facebook API documentation
      const scopes = [
        "public_profile",
        "email",
        "pages_show_list",
        "pages_read_engagement",
        "pages_manage_posts",
        "instagram_basic",
        "instagram_content_publish",
      ];

      const state = Buffer.from(JSON.stringify({ userId })).toString("base64");

      const authUrl = new URL(
        `https://www.facebook.com/${this.apiVersion}/dialog/oauth`
      );
      authUrl.searchParams.append("client_id", this.clientId);
      authUrl.searchParams.append("redirect_uri", this.redirectUri);
      authUrl.searchParams.append("scope", scopes.join(","));
      authUrl.searchParams.append("response_type", "code");
      authUrl.searchParams.append("state", state);

      logger.info("Instagram OAuth URL generated", {
        userId,
        scopes: scopes.join(","),
      });

      return authUrl.toString();
    } catch (error) {
      logger.error("Error generating Instagram OAuth URL:", {
        error: error.message,
        userId,
      });
      throw error;
    }
  }

  /**
   * Exchange authorization code for access token
   * @param {string} code - Authorization code
   * @returns {Object} Token data
   */
  async exchangeCodeForToken(code) {
    try {
      // For Instagram Business API, we use Facebook's token exchange endpoint
      const response = await axios.get(`${this.baseURL}/oauth/access_token`, {
        params: {
          client_id: this.clientId,
          client_secret: this.clientSecret,
          redirect_uri: this.redirectUri,
          code: code,
        },
      });

      logger.info("Instagram (Facebook) access token obtained", {
        hasAccessToken: !!response.data.access_token,
        tokenType: response.data.token_type,
      });

      return response.data;
    } catch (error) {
      logger.error("Error exchanging code for Instagram token:", {
        error: error.message,
        response: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Get Facebook user information (for Instagram Business API)
   * @param {string} accessToken - Access token
   * @returns {Object} User information
   */
  async getUserInfo(accessToken) {
    try {
      // For Instagram Business API, we get Facebook user info first
      const response = await axios.get(`${this.baseURL}/me`, {
        params: {
          fields: "id,name,email,picture",
          access_token: accessToken,
        },
      });

      logger.info("Facebook user info retrieved for Instagram", {
        userId: response.data.id,
        hasEmail: !!response.data.email,
      });

      return response.data;
    } catch (error) {
      logger.error("Error getting Facebook user info for Instagram:", {
        error: error.message,
        response: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Get user's Facebook pages with Instagram Business accounts
   * @param {string} accessToken - Access token
   * @returns {Array} Pages data with Instagram Business account info
   */
  async getUserPages(accessToken) {
    try {
      // Get Facebook pages managed by the user
      const response = await axios.get(`${this.baseURL}/me/accounts`, {
        params: {
          access_token: accessToken,
        },
      });

      const pages = response.data.data || [];
      const pagesWithInstagram = [];

      logger.info("Facebook pages retrieved", {
        totalPages: pages.length,
      });

      // For each page, check if it has an Instagram Business account
      for (const page of pages) {
        try {
          logger.info("Checking page for Instagram Business account", {
            pageId: page.id,
            pageName: page.name,
            pageCategory: page.category,
            hasPageAccessToken: !!page.access_token,
          });

          const instagramAccount = await this.getInstagramBusinessAccount(
            page.access_token,
            page.id
          );

          logger.info("Instagram Business account check result", {
            pageId: page.id,
            pageName: page.name,
            instagramAccount: instagramAccount,
            hasInstagramAccount: !!(instagramAccount && instagramAccount.id),
          });

          if (instagramAccount && instagramAccount.id) {
            // Get Instagram account details
            const instagramProfile = await this.getInstagramUserProfile(
              page.access_token,
              instagramAccount.id
            );

            pagesWithInstagram.push({
              id: instagramAccount.id, // Use Instagram Business Account ID
              name: instagramProfile.name || page.name,
              username: instagramProfile.username,
              picture: {
                data: {
                  url: instagramProfile.profile_picture_url,
                },
              },
              category: page.category,
              access_token: page.access_token,
              page_id: page.id, // Keep Facebook page ID for reference
              instagram_business_account_id: instagramAccount.id,
            });

            logger.info("Instagram Business account found", {
              pageId: page.id,
              pageName: page.name,
              instagramId: instagramAccount.id,
              username: instagramProfile.username,
            });
          }
        } catch (error) {
          logger.warn("Page does not have Instagram Business account", {
            pageId: page.id,
            pageName: page.name,
            error: error.message,
          });
          // Skip pages without Instagram Business accounts
        }
      }

      logger.info("Instagram Business accounts retrieved", {
        totalPages: pages.length,
        instagramAccountsCount: pagesWithInstagram.length,
      });

      return pagesWithInstagram;
    } catch (error) {
      logger.error("Error getting Instagram user pages:", {
        error: error.message,
        response: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Generate Instagram post URL
   * @param {string} accountId - Instagram account ID
   * @param {string} postId - Instagram post ID
   * @returns {string} Instagram post URL
   */
  generatePostUrl(accountId, postId) {
    try {
      // Instagram post URLs follow the pattern: https://www.instagram.com/p/{media-shortcode}/
      // For now, we'll use a generic format since we need the shortcode
      const instagramUrl = `https://www.instagram.com/p/${postId}/`;

      logger.info("Instagram post URL generated", {
        accountId,
        postId,
        instagramUrl,
      });

      return instagramUrl;
    } catch (error) {
      logger.error("Error generating Instagram post URL:", {
        error: error.message,
        accountId,
        postId,
      });
      return null;
    }
  }

  /**
   * Save Instagram access token for a user
   * @param {number} userId - User ID
   * @param {string} accessToken - Facebook access token
   * @param {object} pageData - Facebook page data
   */
  async saveInstagramToken(userId, accessToken, pageData) {
    try {
      // First check if user already has Instagram token
      const existingTokens = await pool.query(
        "SELECT * FROM instagram_tokens WHERE user_id = ?",
        [userId]
      );

      if (existingTokens.length > 0) {
        // Update existing token
        await pool.query(
          "UPDATE instagram_tokens SET access_token = ?, page_data = ?, updated_at = NOW() WHERE user_id = ?",
          [accessToken, JSON.stringify(pageData), userId]
        );
      } else {
        // Insert new token
        await pool.query(
          "INSERT INTO instagram_tokens (user_id, access_token, page_data, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())",
          [userId, accessToken, JSON.stringify(pageData)]
        );
      }

      return { success: true };
    } catch (error) {
      logger.error("Error saving Instagram token", {
        error: error.message,
        stack: error.stack,
        userId,
      });
      throw error;
    }
  }

  /**
   * Get Instagram business account info
   * @param {string} accessToken - Facebook access token
   * @param {string} pageId - Facebook page ID
   */
  async getInstagramBusinessAccount(accessToken, pageId) {
    try {
      logger.info("Requesting Instagram business account for page", {
        pageId,
        url: `${this.baseURL}/${pageId}`,
        hasAccessToken: !!accessToken,
      });

      const response = await axios.get(`${this.baseURL}/${pageId}`, {
        params: {
          fields: "instagram_business_account",
          access_token: accessToken,
        },
      });

      logger.info("Instagram business account API response", {
        pageId,
        responseData: response.data,
        hasInstagramBusinessAccount: !!response.data.instagram_business_account,
        instagramBusinessAccountId:
          response.data.instagram_business_account?.id,
      });

      return response.data.instagram_business_account;
    } catch (error) {
      logger.error("Error getting Instagram business account", {
        error: error.message,
        pageId,
        response: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Get Instagram user profile information
   * @param {string} accessToken - Facebook access token
   * @param {string} igUserId - Instagram user ID
   */
  async getInstagramUserProfile(accessToken, igUserId) {
    try {
      const response = await axios.get(`${this.baseURL}/${igUserId}`, {
        params: {
          fields:
            "username,profile_picture_url,name,biography,website,follows_count,followers_count,media_count",
          access_token: accessToken,
        },
      });

      return response.data;
    } catch (error) {
      logger.error("Error getting Instagram user profile", {
        error: error.message,
        igUserId,
        response: error.response?.data,
      });
      throw error;
    }
  }

  /**
   * Create an Instagram post (supports both images and videos)
   * @param {string} accessToken - Facebook access token
   * @param {string} igUserId - Instagram user ID
   * @param {string} mediaUrl - URL of the media to post
   * @param {string} caption - Post caption
   * @param {string} mediaType - Media type ('image' or 'video')
   */
  async createInstagramPost(
    accessToken,
    igUserId,
    mediaUrl,
    caption,
    mediaType = "image"
  ) {
    try {
      // Step 1: Create media container
      const mediaParams = {
        caption: caption,
        access_token: accessToken,
      };

      // Add appropriate media parameter based on type
      if (mediaType === "video") {
        mediaParams.video_url = mediaUrl;
        mediaParams.media_type = "VIDEO";
      } else {
        mediaParams.image_url = mediaUrl;
        mediaParams.media_type = "IMAGE";
      }

      const containerResponse = await axios.post(
        `${this.baseURL}/${igUserId}/media`,
        null,
        {
          params: mediaParams,
        }
      );

      const containerId = containerResponse.data.id;

      logger.info("Instagram media container created", {
        containerId,
        igUserId,
        mediaType,
      });

      // Step 2: Publish the media
      const publishResponse = await axios.post(
        `${this.baseURL}/${igUserId}/media_publish`,
        null,
        {
          params: {
            creation_id: containerId,
            access_token: accessToken,
          },
        }
      );

      logger.info("Instagram post published successfully", {
        mediaId: publishResponse.data.id,
        containerId,
        igUserId,
        mediaType,
      });

      return {
        id: publishResponse.data.id,
        containerId: containerId,
        success: true,
      };
    } catch (error) {
      logger.error("Error creating Instagram post", {
        error: error.message,
        response: error.response?.data,
        igUserId,
        mediaType,
        mediaUrl,
      });
      throw error;
    }
  }

  /**
   * Save Instagram post to database
   * @param {number} userId - User ID
   * @param {number} businessId - Business ID
   * @param {string} mediaId - Instagram media ID
   * @param {string} caption - Post caption
   * @param {string} imageUrl - URL of the image posted
   */
  async saveInstagramPost(userId, businessId, mediaId, caption, imageUrl) {
    try {
      const query = `
        INSERT INTO instagram_posts 
        (user_id, business_id, media_id, caption, image_url, created_at)
        VALUES (?, ?, ?, ?, ?, NOW())
      `;

      await pool.query(query, [userId, businessId, mediaId, caption, imageUrl]);

      logger.info("Instagram post saved to database", {
        userId,
        businessId,
        mediaId,
      });

      return { success: true };
    } catch (error) {
      logger.error("Error saving Instagram post to database", {
        error: error.message,
        stack: error.stack,
        userId,
        businessId,
        mediaId,
      });
      throw error;
    }
  }
}

module.exports = InstagramService;
