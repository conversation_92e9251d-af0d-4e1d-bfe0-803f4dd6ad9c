.text-capitalize {
    text-transform: capitalize;
}

.border_raduis {
    border-radius: 6px !important;
}

/* Local Falcon responsive styles */
.local-falcon-container {
    width: 100%;
}

.local-falcon-controls-panel {
    height: auto;
    min-height: 500px;
    box-sizing: border-box;
}

.local-falcon-map-panel {
    height: auto;
    overflow: hidden;
    box-sizing: border-box;
}

/* Ensure the Paper component inside map panel takes full height */
.local-falcon-map-panel .MuiPaper-root {
    height: 100% !important;
    box-sizing: border-box;
}

/* Mobile responsive adjustments */
@media (max-width: 960px) {
    .local-falcon-controls-panel {
        min-height: auto;
        margin-bottom: 16px;
    }

    .local-falcon-map-panel {
        min-height: 400px;
    }
}

/* Ensure proper spacing on smaller screens */
@media (max-width: 600px) {
    .local-falcon-container {
        padding: 8px;
    }

    .local-falcon-map-panel {
        min-height: 350px;
    }
}