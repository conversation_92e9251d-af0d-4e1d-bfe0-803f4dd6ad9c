const autoReplyModel = require("../models/autoReplyModel");
const backendApiService = require("./backendApiService");
const reviewSyncService = require("./reviewSyncService");
const logger = require("../utils/logger");

class AutoReplyService {
  constructor() {
    this.isProcessing = false;
    this.processedCount = 0;
    this.errorCount = 0;
  }

  /**
   * Main method to process auto-replies
   */
  async processAutoReplies() {
    if (this.isProcessing) {
      logger.logAutoReply("SKIP", { reason: "Already processing" });
      return;
    }

    this.isProcessing = true;
    this.processedCount = 0;
    this.errorCount = 0;

    try {
      logger.logAutoReply("START", { timestamp: new Date().toISOString() });

      // Get all auto-reply configurations (business/account/location level)
      const autoReplyConfigs =
        await autoReplyModel.getEnabledAutoReplyBusinesses();

      if (autoReplyConfigs.length === 0) {
        logger.logAutoReply("NO_CONFIGS", {
          message: "No auto-reply configurations enabled",
        });
        return;
      }

      logger.logAutoReply("CONFIGS_FOUND", { count: autoReplyConfigs.length });

      // Process each configuration
      for (const config of autoReplyConfigs) {
        await this.processAutoReplyConfig(config);
      }

      logger.logAutoReply("COMPLETE", {
        processedCount: this.processedCount,
        errorCount: this.errorCount,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error("Error in processAutoReplies:", error);
      this.errorCount++;
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process auto-replies for a specific business
   */
  async processBusinessAutoReplies(business) {
    try {
      logger.logAutoReply("BUSINESS_START", {
        businessId: business.business_id,
        businessName: business.businessName,
      });

      // Check if we should process based on business hours
      if (
        business.only_business_hours &&
        !autoReplyModel.isWithinBusinessHours(
          business.business_hours_start,
          business.business_hours_end
        )
      ) {
        logger.logAutoReply("SKIP_BUSINESS_HOURS", {
          businessId: business.business_id,
          currentTime: new Date().toTimeString(),
          businessHours: `${business.business_hours_start} - ${business.business_hours_end}`,
        });
        return;
      }

      // Get pending reviews for this business
      const reviews = await autoReplyModel.getPendingReviews(
        business.business_id,
        business.enabled_star_ratings,
        business.delay_minutes
      );

      if (reviews.length === 0) {
        logger.logAutoReply("NO_PENDING_REVIEWS", {
          businessId: business.business_id,
        });
        return;
      }

      logger.logAutoReply("REVIEWS_FOUND", {
        businessId: business.business_id,
        reviewCount: reviews.length,
      });

      // Process each review
      for (const review of reviews) {
        await this.processReviewAutoReply(business, review);
      }
    } catch (error) {
      logger.error("Error processing business auto-replies:", {
        businessId: business.business_id,
        error: error.message,
      });
      this.errorCount++;
    }
  }

  /**
   * Process auto-replies for a specific configuration (business/account/location)
   */
  async processAutoReplyConfig(config) {
    try {
      logger.logAutoReply("CONFIG_START", {
        businessId: config.business_id,
        businessName: config.businessName,
        accountId: config.account_id,
        locationId: config.location_id,
        hierarchyLevel: config.hierarchy_level,
      });

      // Check if we should process based on business hours
      if (
        config.only_business_hours &&
        !autoReplyModel.isWithinBusinessHours(
          config.business_hours_start,
          config.business_hours_end
        )
      ) {
        logger.logAutoReply("SKIP_BUSINESS_HOURS", {
          businessId: config.business_id,
          accountId: config.account_id,
          locationId: config.location_id,
          currentTime: new Date().toTimeString(),
          businessHours: `${config.business_hours_start} - ${config.business_hours_end}`,
        });
        return;
      }

      // Sync reviews for this location before processing auto replies
      await this.syncReviewsForAutoReply(config);

      // Get pending reviews for this configuration
      const reviews = await autoReplyModel.getPendingReviews(config);

      if (reviews.length === 0) {
        logger.logAutoReply("NO_PENDING_REVIEWS", {
          businessId: config.business_id,
          accountId: config.account_id,
          locationId: config.location_id,
        });
        return;
      }

      logger.logAutoReply("REVIEWS_FOUND", {
        businessId: config.business_id,
        accountId: config.account_id,
        locationId: config.location_id,
        reviewCount: reviews.length,
      });

      // Process each review
      for (const review of reviews) {
        await this.processReviewAutoReply(config, review);
      }
    } catch (error) {
      logger.error("Error processing auto-reply config:", {
        businessId: config.business_id,
        accountId: config.account_id,
        locationId: config.location_id,
        error: error.message,
      });
      this.errorCount++;
    }
  }

  /**
   * Sync reviews for a specific location before processing auto replies
   */
  async syncReviewsForAutoReply(config) {
    try {
      // Only sync if we have location-specific or account-specific configuration
      if (!config.location_id && !config.account_id) {
        logger.logAutoReply("SKIP_REVIEW_SYNC", {
          businessId: config.business_id,
          reason: "Business-level config - no specific location to sync",
        });
        return;
      }

      logger.logAutoReply("REVIEW_SYNC_START", {
        businessId: config.business_id,
        accountId: config.account_id,
        locationId: config.location_id,
        hierarchyLevel: config.hierarchy_level,
      });

      // Get OAuth token for the account
      const oauthToken = await this.getOAuthTokenForAccount(config.account_id);
      if (!oauthToken) {
        logger.logAutoReply("REVIEW_SYNC_NO_TOKEN", {
          businessId: config.business_id,
          accountId: config.account_id,
          locationId: config.location_id,
        });
        return;
      }

      // If location-specific, sync only that location
      if (config.location_id) {
        const location = await this.getLocationDetails(config.location_id);
        if (location) {
          await reviewSyncService.syncReviewsForLocation(oauthToken, location);
          logger.logAutoReply("REVIEW_SYNC_LOCATION_COMPLETE", {
            businessId: config.business_id,
            accountId: config.account_id,
            locationId: config.location_id,
            locationName: location.gmbLocationName,
          });
        }
      } else if (config.account_id) {
        // If account-specific, sync all locations for that account
        const locations = await this.getLocationsForAccount(config.account_id);
        for (const location of locations) {
          await reviewSyncService.syncReviewsForLocation(oauthToken, location);
        }
        logger.logAutoReply("REVIEW_SYNC_ACCOUNT_COMPLETE", {
          businessId: config.business_id,
          accountId: config.account_id,
          locationsCount: locations.length,
        });
      }
    } catch (error) {
      logger.error("Error syncing reviews for auto reply:", {
        businessId: config.business_id,
        accountId: config.account_id,
        locationId: config.location_id,
        error: error.message,
      });
      // Don't throw error - continue with auto reply processing even if sync fails
    }
  }

  /**
   * Process auto-reply for a specific review
   */
  async processReviewAutoReply(config, review) {
    try {
      logger.logAutoReply("REVIEW_START", {
        businessId: config.business_id,
        accountId: config.account_id,
        locationId: config.location_id,
        reviewId: review.reviewId,
        starRating: review.star_rating_numeric,
        aiAutoReplyEnabled: config.enable_ai_auto_reply,
      });

      let replyContent;
      let templateId = null;
      // Check if AI auto reply is enabled
      if (config.enable_ai_auto_reply) {
        logger.logAutoReply("AI_REPLY_START", {
          businessId: config.business_id,
          reviewId: review.reviewId,
          reviewText: review.comment,
          starRating: review.star_rating_numeric,
        });

        // Generate AI reply
        replyContent = await this.generateAIReply(review);

        if (!replyContent) {
          logger.logAutoReply("AI_REPLY_FAILED", {
            businessId: config.business_id,
            reviewId: review.reviewId,
          });

          await autoReplyModel.logAutoReplyAttempt(
            review.reviewId,
            config.business_id,
            config.account_id,
            config.location_id,
            null,
            null,
            "failed",
            "AI reply generation failed"
          );
          return;
        }

        logger.logAutoReply("AI_REPLY_SUCCESS", {
          businessId: config.business_id,
          reviewId: review.reviewId,
          replyLength: replyContent.length,
        });
      } else {
        // Use template-based reply (existing logic)
        const template = await autoReplyModel.getReplyTemplate(
          config,
          review.star_rating_numeric
        );

        if (!template) {
          logger.logAutoReply("NO_TEMPLATE", {
            businessId: config.business_id,
            accountId: config.account_id,
            locationId: config.location_id,
            reviewId: review.reviewId,
            starRating: review.star_rating_numeric,
          });

          await autoReplyModel.logAutoReplyAttempt(
            review.reviewId,
            config.business_id,
            config.account_id,
            config.location_id,
            null,
            null,
            "skipped",
            "No template found for star rating"
          );
          return;
        }

        // Generate personalized reply content
        replyContent = this.generateReplyContent(template, review);
        templateId = template.id;
      }

      // Log the attempt before making the API call
      await autoReplyModel.logAutoReplyAttempt(
        review.reviewId,
        config.business_id,
        config.account_id,
        config.location_id,
        templateId,
        replyContent,
        "pending"
      );

      // Here you would make the actual API call to Google My Business
      // For now, we'll simulate the reply posting
      const success = await this.postReplyToGMB(review, replyContent);

      if (success) {
        await autoReplyModel.logAutoReplyAttempt(
          review.reviewId,
          config.business_id,
          config.account_id,
          config.location_id,
          templateId,
          replyContent,
          "success"
        );

        this.processedCount++;

        logger.logAutoReply("REVIEW_SUCCESS", {
          businessId: config.business_id,
          accountId: config.account_id,
          locationId: config.location_id,
          reviewId: review.reviewId,
          templateId: templateId,
          aiGenerated: config.enable_ai_auto_reply,
        });
      } else {
        throw new Error("Failed to post reply to GMB");
      }
    } catch (error) {
      logger.error("Error processing review auto-reply:", {
        businessId: config.business_id,
        accountId: config.account_id,
        locationId: config.location_id,
        reviewId: review.reviewId,
        error: error.message,
      });

      await autoReplyModel.logAutoReplyAttempt(
        review.reviewId,
        config.business_id,
        config.account_id,
        config.location_id,
        template?.id || null,
        null,
        "failed",
        error.message
      );

      this.errorCount++;
    }
  }

  /**
   * Generate AI reply for a review
   */
  async generateAIReply(review) {
    try {
      // Call the main backend API to generate AI reply
      const result = await backendApiService.generateAIReply(
        review.comment || "Thank you for your review!",
        review.star_rating_numeric || 5
      );

      if (result.success && result.data && result.data.generatedText) {
        return result.data.generatedText;
      } else {
        logger.logAutoReply("AI_REPLY_API_FAILED", {
          reviewId: review.reviewId,
          error: result.error || "No generated text returned",
        });
        return null;
      }
    } catch (error) {
      logger.error("Error generating AI reply:", {
        reviewId: review.reviewId,
        error: error.message,
      });
      return null;
    }
  }

  /**
   * Generate personalized reply content from template
   */
  generateReplyContent(template, review) {
    let content = template.template_content;

    // Replace placeholders with actual data
    const replacements = {
      "{reviewer_name}": review.reviewerName || "Valued Customer",
      "{business_name}": review.gmbLocationName || "",
      "{star_rating}": review.star_rating_numeric,
      "{current_date}": new Date().toLocaleDateString(),
      "{review_text}": review.review || "",
    };

    for (const [placeholder, value] of Object.entries(replacements)) {
      content = content.replace(new RegExp(placeholder, "g"), value);
    }

    return content;
  }

  /**
   * Post reply to Google My Business via main backend API
   */
  async postReplyToGMB(review, replyContent) {
    try {
      logger.logAutoReply("GMB_API_CALL_START", {
        reviewId: review.reviewId,
        locationId: review.gmbLocationId,
        accountId: review.gmbAccountId,
        replyLength: replyContent.length,
      });

      // Call the main backend API to post the review reply
      const result = await backendApiService.replyToReview(
        review,
        replyContent
      );

      if (result.success) {
        logger.logAutoReply("GMB_API_CALL_SUCCESS", {
          reviewId: review.reviewId,
          status: result.status,
          message: result.data?.message,
        });
        return true;
      } else {
        logger.logAutoReply("GMB_API_CALL_FAILED", {
          reviewId: review.reviewId,
          status: result.status,
          error: result.error,
        });
        return false;
      }
    } catch (error) {
      logger.error("Error posting reply to GMB:", {
        reviewId: review.reviewId,
        error: error.message,
        stack: error.stack,
      });
      return false;
    }
  }

  /**
   * Get service statistics
   */
  getStats() {
    return {
      isProcessing: this.isProcessing,
      processedCount: this.processedCount,
      errorCount: this.errorCount,
      lastRun: new Date().toISOString(),
    };
  }

  /**
   * Get OAuth token for a specific account
   */
  async getOAuthTokenForAccount(accountId) {
    try {
      const database = require("../config/database");
      const query = `
        SELECT
          gmbAccountId,
          userId,
          accessToken,
          refreshToken
        FROM gmb_oauth_tokens
        WHERE gmbAccountId = ? AND statusId = 1
        LIMIT 1
      `;

      const tokens = await database.query(query, [accountId]);
      return tokens.length > 0 ? tokens[0] : null;
    } catch (error) {
      logger.error("Error fetching OAuth token:", {
        accountId,
        error: error.message,
      });
      return null;
    }
  }

  /**
   * Get location details by location ID
   */
  async getLocationDetails(locationId) {
    try {
      const database = require("../config/database");
      const query = `
        SELECT
          id,
          gmbLocationId,
          gmbLocationName,
          gmbAccountId,
          statusId
        FROM gmb_locations
        WHERE gmbLocationId = ? AND statusId = 1
        LIMIT 1
      `;

      const locations = await database.query(query, [locationId]);
      return locations.length > 0 ? locations[0] : null;
    } catch (error) {
      logger.error("Error fetching location details:", {
        locationId,
        error: error.message,
      });
      return null;
    }
  }

  /**
   * Get all locations for a specific account
   */
  async getLocationsForAccount(accountId) {
    try {
      const database = require("../config/database");
      const query = `
        SELECT
          id,
          gmbLocationId,
          gmbLocationName,
          gmbAccountId,
          statusId
        FROM gmb_locations
        WHERE gmbAccountId = ? AND statusId = 1
        ORDER BY gmbLocationName
      `;

      const locations = await database.query(query, [accountId]);
      return locations || [];
    } catch (error) {
      logger.error("Error fetching locations for account:", {
        accountId,
        error: error.message,
      });
      return [];
    }
  }
}

module.exports = new AutoReplyService();
