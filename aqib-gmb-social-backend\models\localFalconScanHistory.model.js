const pool = require("../config/db");

class LocalFalconScanHistoryModel {
  /**
   * Create a new scan history record
   */
  static async create(scanData) {
    const query = `
      INSERT INTO local_falcon_scan_history (
        user_id, business_name, keyword, place_id, latitude, longitude,
        grid_size, radius_km, location_name, location_address, location_city,
        location_state, location_country, total_points, found_points,
        visibility_percent, average_ranking_position, average_top_ranking_position,
        share_of_local_voice, request_data, response_data, scan_status,
        scan_duration_ms, error_message
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const values = [
      scanData.userId,
      scanData.businessName,
      scanData.keyword,
      scanData.placeId,
      scanData.latitude,
      scanData.longitude,
      scanData.gridSize || 5,
      scanData.radiusKm || 10,
      scanData.locationName,
      scanData.locationAddress,
      scanData.locationCity,
      scanData.locationState,
      scanData.locationCountry,
      scanData.totalPoints,
      scanData.foundPoints,
      scanData.visibilityPercent,
      scanData.averageRankingPosition,
      scanData.averageTopRankingPosition,
      scanData.shareOfLocalVoice,
      JSON.stringify(scanData.requestData),
      JSON.stringify(scanData.responseData),
      scanData.scanStatus || "completed",
      scanData.scanDurationMs,
      scanData.errorMessage,
    ];

    const result = await pool.query(query, values);
    return result.insertId;
  }

  /**
   * Update scan history record
   */
  static async update(id, updateData) {
    const fields = [];
    const values = [];

    Object.keys(updateData).forEach((key) => {
      if (key === "requestData" || key === "responseData") {
        fields.push(`${key} = ?`);
        values.push(JSON.stringify(updateData[key]));
      } else {
        fields.push(`${key} = ?`);
        values.push(updateData[key]);
      }
    });

    values.push(id);

    const query = `
      UPDATE local_falcon_scan_history 
      SET ${fields.join(", ")}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;

    const result = await pool.query(query, values);
    return result.affectedRows > 0;
  }

  /**
   * Get scan history for a user with pagination
   */
  static async getByUserId(userId, options = {}) {
    const {
      page = 1,
      limit = 20,
      sortBy = "created_at",
      sortOrder = "DESC",
      status = null,
      keyword = null,
      businessName = null,
    } = options;

    const offset = (page - 1) * limit;
    let whereConditions = ["user_id = ?"];
    let values = [userId];

    if (status) {
      whereConditions.push("scan_status = ?");
      values.push(status);
    }

    if (keyword) {
      whereConditions.push("keyword LIKE ?");
      values.push(`%${keyword}%`);
    }

    if (businessName) {
      whereConditions.push("business_name LIKE ?");
      values.push(`%${businessName}%`);
    }

    const whereClause = whereConditions.join(" AND ");

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM local_falcon_scan_history 
      WHERE ${whereClause}
    `;
    const countResult = await pool.query(countQuery, values);
    const total = countResult[0].total;

    // Get records
    const query = `
      SELECT
        id, user_id, business_name, keyword, place_id, latitude, longitude,
        grid_size, radius_km, location_name, location_address, location_city,
        location_state, location_country, total_points, found_points,
        visibility_percent, average_ranking_position, average_top_ranking_position,
        share_of_local_voice, scan_status, scan_duration_ms, error_message,
        created_at, updated_at
      FROM local_falcon_scan_history
      WHERE ${whereClause}
      ORDER BY ${sortBy} ${sortOrder}
      LIMIT ? OFFSET ?
    `;

    values.push(limit, offset);
    const rows = await pool.query(query, values);

    return {
      data: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get all scan history (admin only) with user details
   */
  static async getAll(options = {}) {
    const {
      page = 1,
      limit = 20,
      sortBy = "created_at",
      sortOrder = "DESC",
      status = null,
      keyword = null,
      businessName = null,
      userId = null,
    } = options;

    const offset = (page - 1) * limit;
    let whereConditions = [];
    let values = [];

    if (status) {
      whereConditions.push("h.scan_status = ?");
      values.push(status);
    }

    if (keyword) {
      whereConditions.push("h.keyword LIKE ?");
      values.push(`%${keyword}%`);
    }

    if (businessName) {
      whereConditions.push("h.business_name LIKE ?");
      values.push(`%${businessName}%`);
    }

    if (userId) {
      whereConditions.push("h.user_id = ?");
      values.push(userId);
    }

    const whereClause =
      whereConditions.length > 0
        ? "WHERE " + whereConditions.join(" AND ")
        : "";

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM local_falcon_scan_history h
      LEFT JOIN users u ON h.user_id = u.id
      ${whereClause}
    `;
    const countResult = await pool.query(countQuery, values);
    const total = countResult[0].total;

    // Get records with user details
    const query = `
      SELECT
        h.id, h.user_id, h.business_name, h.keyword, h.place_id, h.latitude, h.longitude,
        h.grid_size, h.radius_km, h.location_name, h.location_address, h.location_city,
        h.location_state, h.location_country, h.total_points, h.found_points,
        h.visibility_percent, h.average_ranking_position, h.average_top_ranking_position,
        h.share_of_local_voice, h.scan_status, h.scan_duration_ms, h.error_message,
        h.created_at, h.updated_at,
        u.name as user_name, u.email as user_email
      FROM local_falcon_scan_history h
      LEFT JOIN users u ON h.user_id = u.id
      ${whereClause}
      ORDER BY h.${sortBy} ${sortOrder}
      LIMIT ? OFFSET ?
    `;

    values.push(limit, offset);
    const rows = await pool.query(query, values);

    return {
      data: rows,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Get scan history by ID with full data
   */
  static async getById(id, userId = null) {
    let query = `
      SELECT 
        h.*,
        u.name as user_name, u.email as user_email
      FROM local_falcon_scan_history h
      LEFT JOIN users u ON h.user_id = u.id
      WHERE h.id = ?
    `;
    let values = [id];

    if (userId) {
      query += " AND h.user_id = ?";
      values.push(userId);
    }

    const rows = await pool.query(query, values);

    if (rows.length === 0) {
      return null;
    }

    const record = rows[0];

    // Parse JSON data if it's a string
    if (record.request_data) {
      if (typeof record.request_data === "string") {
        try {
          record.request_data = JSON.parse(record.request_data);
        } catch (error) {
          console.error("Error parsing request_data JSON:", error);
          record.request_data = null;
        }
      }
      // If it's already an object, keep it as is
    }
    if (record.response_data) {
      if (typeof record.response_data === "string") {
        try {
          record.response_data = JSON.parse(record.response_data);
        } catch (error) {
          console.error("Error parsing response_data JSON:", error);
          record.response_data = null;
        }
      }
      // If it's already an object, keep it as is
    }

    return record;
  }

  /**
   * Delete scan history record
   */
  static async delete(id, userId = null) {
    let query = "DELETE FROM local_falcon_scan_history WHERE id = ?";
    let values = [id];

    if (userId) {
      query += " AND user_id = ?";
      values.push(userId);
    }

    const result = await pool.query(query, values);
    return result.affectedRows > 0;
  }

  /**
   * Get scan statistics for dashboard
   */
  static async getStats(userId = null) {
    let whereClause = userId ? "WHERE user_id = ?" : "";
    let values = userId ? [userId] : [];

    const query = `
      SELECT 
        COUNT(*) as total_scans,
        COUNT(CASE WHEN scan_status = 'completed' THEN 1 END) as completed_scans,
        COUNT(CASE WHEN scan_status = 'failed' THEN 1 END) as failed_scans,
        AVG(CASE WHEN scan_status = 'completed' THEN visibility_percent END) as avg_visibility,
        AVG(CASE WHEN scan_status = 'completed' THEN average_ranking_position END) as avg_ranking,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as scans_last_30_days
      FROM local_falcon_scan_history
      ${whereClause}
    `;

    const rows = await pool.query(query, values);
    return rows[0];
  }
}

module.exports = LocalFalconScanHistoryModel;
