// Add this at the top of your file to force HMR to work
if (import.meta.hot) {
  import.meta.hot.accept();
}

import React from "react";
import {
  FunctionComponent,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import { Formik, Form } from "formik";
import PageProps from "../../../models/PageProps.interface";

//Widgets
import CloseIcon from "@mui/icons-material/Close";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import CardMedia from "@mui/material/CardMedia";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import Switch from "@mui/material/Switch";
import Grid from "@mui/material/Grid";
import {
  Chip,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  IconButton,
  Button,
  // CloseIcon,
  FormControlLabel,
  Checkbox,
  InputAdornment,
  MenuItem,
} from "@mui/material";

//Image Import
import businessPreview from "../../../assets/businessManagement/businessPreview.png?url";
import noCoverImage from "../../../assets/businessManagement/No_Cover.jpg?url";
import businessSummaryMobileImage from "../../../assets/businessManagement/businessSummaryMobile.png?url";
import picture1Image from "../../../assets/dashboard/Picture1.png?url";
import picture2Image from "../../../assets/dashboard/Picture2.png?url";
import picture3Image from "../../../assets/dashboard/Picture3.png?url";
import InfoOutlinedIcon from "@mui/icons-material/InfoOutlined";

//Components
import LeftMenuComponent from "../../../components/leftMenu/leftMenu.component";
import LinearProgressWithLabel from "../../../components/LinearProgressWithLabel/LinearProgressWithLabel.component";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";

//Css
import "../businessSummary/businessSummary.screen.style.css";

//Icons
import LockOpenIcon from "@mui/icons-material/LockOpen";
import VerifiedRoundedIcon from "@mui/icons-material/VerifiedRounded";
import DoneRoundedIcon from "@mui/icons-material/DoneRounded";
import { useParams } from "react-router-dom";
import { LoadingContext } from "../../../context/loading.context";
import { useDispatch, useSelector } from "react-redux";
import LocationService from "../../../services/location/location.service";
import { ToastContext } from "../../../context/toast.context";
import { ToastSeverity } from "../../../constants/toastSeverity.constant";
import ApplicationHelperService from "../../../services/ApplicationHelperService";
import { MISSING_INFORMATION } from "../../../constants/application.constant";
import { MessageConstants } from "../../../constants/message.constant";
import CategoryDisplay from "../../../components/categoryDisplay/categoryDisplay.component";
import ServiceItemsDisplay from "../../../components/serviceItemsDisplay/serviceItemsDisplay.component";
import RegularHoursTable from "../../../components/regularHoursTable/regularHoursTable.component";
import LogoPhotoSection from "../../../components/logosPhotosDisplay/logsPhotosDisplay.component";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import CancelOutlinedIcon from "@mui/icons-material/CancelOutlined";
import LocalPhoneRoundedIcon from "@mui/icons-material/LocalPhoneRounded";
import CategoryRoundedIcon from "@mui/icons-material/CategoryRounded";
import LocationOnRoundedIcon from "@mui/icons-material/LocationOnRounded";
import LanguageRoundedIcon from "@mui/icons-material/LanguageRounded";
import EventAvailableRoundedIcon from "@mui/icons-material/EventAvailableRounded";
import CampaignRoundedIcon from "@mui/icons-material/CampaignRounded";
import ReviewsRoundedIcon from "@mui/icons-material/ReviewsRounded";
import IconOnAvailability from "../../../components/iconOnAvailability/iconOnAvailability.component";
import ServiceAreaList from "../../../components/serviceAreaList/serviceAreaList.component";
import AddLocationIcon from "@mui/icons-material/AddLocation";
import * as yup from "yup";
import ClearIcon from "@mui/icons-material/Clear";
import AddIcon from "@mui/icons-material/Add";
import { Divider } from "@mui/material";
import { JoinFullSharp } from "@mui/icons-material";

const label = { inputProps: { "aria-label": "Switch demo" } };

interface StatusCardProps {
  title: string;
  isComplete: boolean;
  count?: number;
}

export interface IMissingInformationContent {
  title: string;
  isComplete: boolean;
  keyIdentifier: string;
  remarks: string;
}

interface IPhoneNumbers {
  primaryPhone: string;
  additionalPhones: string[];
}

const BusinessSummary: FunctionComponent<PageProps> = ({ title }) => {
  const { setLoading } = useContext(LoadingContext);
  const { setToastConfig, setOpen } = useContext(ToastContext);
  const dispatch = useDispatch();
  // Business Categories
  const [isCategoriesModalOpen, setIsCategoriesModalOpen] = useState(false);
  const [categories, setCategories] = useState([
    { name: "Eye Care Clinic", isPrimary: true },
    { name: "Lasik surgeon", isPrimary: false },
    { name: "Ophthalmologist", isPrimary: false },
    { name: "Ophthalmology Clinic", isPrimary: false },
    { name: "Paediatric Ophthalmologist", isPrimary: false },
  ]);
  // Business Name Modal

  const handleCloseBusniessNameModal = () => {
    setIsModalOpen(false);
  };

  // const handleOpenAddCategoryModal = () => {
  //   setIsAddCategoryModalOpen(true);
  // };

  // const handleCloseAddCategoryModal = () => {
  //   setIsAddCategoryModalOpen(false);
  // };

  // const handleAddCategory = (categoryName: string) => {
  //   console.log("Added category:", categoryName);
  //   setIsAddCategoryModalOpen(false);
  // };

  // Business Categories Modal
  const handleOpenCategoriesModal = () => {
    setIsCategoriesModalOpen(true);
  };

  const handleCloseCategoriesModal = () => {
    setIsCategoriesModalOpen(false);
  };
  //Dynamic Tabs Handling
  const [selectedTab, setSelectedTab] = React.useState("basicBusinessDetails");
  const [isPhoneNumberModalOpen, setIsPhoneNumberModalOpen] = useState(false);
  const [locationSummary, setLocationSummary] = useState<any | null>();
  const [missingInformation, setMissingInformation] = useState<
    IMissingInformationContent[]
  >([]);
  const [coverPhoto, setCoverPhoto] = useState<string>(noCoverImage);
  const [profilePhoto, setProfilePhoto] = useState<string>(noCoverImage);
  const [phoneNumbers, setPhoneNumbers] = useState<{
    primaryPhone: string;
    additionalPhones: string[];
  }>({
    primaryPhone: "080 6821 2859",
    additionalPhones: ["089048 33434"],
  });

  const _locationService = new LocationService(dispatch);
  const _applicationHelperService = new ApplicationHelperService({});
  const handleChange = (event: React.SyntheticEvent, newValue: string) => {
    setSelectedTab(newValue);
  };
  const [progress, setProgress] = React.useState<number>(0);
  const { businessId, accountId, locationId } = useParams();

  const handleOpenPhoneNumberModal = () => {
    // Set initial phone numbers from location data
    if (locationSummary && locationSummary.phoneNumbers) {
      setPhoneNumbers({
        primaryPhone: locationSummary.phoneNumbers.primaryPhone || "",
        additionalPhones: locationSummary.phoneNumbers.additionalPhones || [],
      });
    }
    setIsPhoneNumberModalOpen(true);
  };

  const handleClosePhoneNumberModal = () => {
    setIsPhoneNumberModalOpen(false);
  };

  // Business Location Code
  const [isBusinessLocationModalOpen, setIsBusinessLocationModalOpen] =
    useState(false);
  const [businessLocationData, setBusinessLocationData] = useState({
    showAddress: true,
    country: "India",
    streetAddress: "9, KHM Block, RT nagar main road",
    streetAddressLine2: "Ganganagar",
    additionalAddressLines: [],
    city: "Bangalore",
    pincode: "560032",
    state: "Karnataka",
    latitude: 13.0234,
    longitude: 77.5938,
  });

  const websiteValidationSchema = yup.object({
    websiteUrl: yup
      .string()
      .required("Website URL is required")
      .url("Please enter a valid URL (e.g., https://www.example.com)"),
  });
  // Business Location Related Code
  const businessLocationValidationSchema = yup.object({
    country: yup.string().required("Country is required"),
    streetAddress: yup.string().required("Street address is required"),
    streetAddressLine2: yup.string(),
    additionalAddressLines: yup.array().of(yup.string()),
    city: yup.string().required("City is required"),
    pincode: yup.string().required("Pincode is required"),
    state: yup.string().required("State is required"),
  });
  const handleOpenBusinessLocationModal = () => {
    setIsBusinessLocationModalOpen(true);
  };

  const handleCloseBusinessLocationModal = () => {
    setIsBusinessLocationModalOpen(false);
  };

  const handleSavePhoneNumbers = (values: {
    primaryPhone: string;
    additionalPhones: string[];
  }) => {
    setPhoneNumbers(values);
    console.log("Phone numbers saved:", values);
    handleClosePhoneNumberModal();
  };
  const phoneNumberValidationSchema = yup.object({
    primaryPhone: yup
      .string()
      .required("Primary phone number is required")
      .matches(
        /^[0-9\s]+$/,
        "Phone number should only contain digits and spaces"
      )
      .min(10, "Phone number must be at least 10 digits"),
    additionalPhones: yup.array().of(
      yup
        .string()
        .matches(
          /^[0-9\s]+$/,
          "Phone number should only contain digits and spaces"
        )
        .min(10, "Phone number must be at least 10 digits")
    ),
  });

  useEffect(() => {
    console.log(
      "Business Id",
      businessId,
      "GMB Account Id: ",
      accountId,
      "-GMB Location Id: ",
      locationId
    );

    getLocationSummary();
  }, []);

  const getLocationSummary = async () => {
    try {
      setLoading(true);
      const getLocationDetailsRequest = {
        "x-gmb-account-id": accountId,
        "x-gmb-business-id": businessId,
        "x-gmb-location-id": locationId,
      };

      const locationSummaryResponse: any =
        await _locationService.getLocationSummary(getLocationDetailsRequest);

      if (locationSummaryResponse.success) {
        // var locationSummary =
        // setToastConfig(
        //   ToastSeverity.Success,
        //   locationSummaryResponse.message,
        //   true
        // );
        setLocationSummary(locationSummaryResponse.data);
        const coverPhoto =
          locationSummaryResponse.data.mediaInfo.mediaItems.find(
            (item: any) => item.locationAssociation?.category === "COVER"
          );

        const profilePhoto =
          locationSummaryResponse.data.mediaInfo.mediaItems.find(
            (item: any) => item.locationAssociation?.category === "PROFILE"
          );

        console.log(coverPhoto);

        // Optional: get just the googleUrl
        if (coverPhoto) {
          console.log("Cover Photo URL:", coverPhoto.googleUrl);
          const base64Cover = await _locationService.getGoogleImageBase64(
            coverPhoto.googleUrl
          );
          setCoverPhoto(base64Cover.base64);
        }

        if (profilePhoto) {
          console.log("Profile Photo URL:", profilePhoto.thumbnailUrl);
          const base64Image = await _locationService.getGoogleImageBase64(
            profilePhoto.googleUrl
          );
          setProfilePhoto(base64Image.base64);
        }
      } else {
        setToastConfig(
          ToastSeverity.Error,
          locationSummaryResponse.message,
          true
        );
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const calculateProgress = () => {
    const total: number = missingInformation.length;
    const completed: number = missingInformation.filter(
      (x: IMissingInformationContent) => x.isComplete
    ).length;
    const percentage = (completed / total) * 100;
    return percentage.toFixed(2) as any as number;
  };

  const getAddress = () => {
    return locationSummary
      ? `${_applicationHelperService.toTitleCase(
          locationSummary.storefrontAddress.addressLines.join(", ")
        )}${
          locationSummary.storefrontAddress.locality
            ? ", " +
              _applicationHelperService.toTitleCase(
                locationSummary.storefrontAddress.locality
              )
            : ""
        }${
          locationSummary.storefrontAddress.administrativeArea
            ? ", " +
              _applicationHelperService.toTitleCase(
                locationSummary.storefrontAddress.administrativeArea
              )
            : ""
        } ${
          locationSummary.storefrontAddress.postalCode
            ? ", " + locationSummary.storefrontAddress.postalCode
            : ""
        }`
      : "";
  };

  useEffect(() => {
    if (locationSummary) {
      performMissingInformationOperation();
    }
  }, [locationSummary]);

  const getMissedPhoneNumber = () => {
    let missPhoneNumber: IMissingInformationContent = {
      title: MISSING_INFORMATION.PhoneNumber.title,
      keyIdentifier: MISSING_INFORMATION.PhoneNumber.key,
      isComplete: false,
      remarks: "",
    };
    try {
      if (
        Object.keys(locationSummary.phoneNumbers).length > 0 &&
        locationSummary.phoneNumbers.primaryPhone
      ) {
        missPhoneNumber.isComplete = true;
        missPhoneNumber.remarks = MessageConstants.NoListingsMissing;
      } else {
        missPhoneNumber.isComplete = false;
        missPhoneNumber.remarks = MessageConstants.ListingsMissing;
      }
    } catch (error) {}

    return missPhoneNumber;
  };

  const getMissedWebSiteLink = () => {
    let missWebSiteLink = {
      title: MISSING_INFORMATION.WebsiteLink.title,
      keyIdentifier: MISSING_INFORMATION.WebsiteLink.key,
      isComplete: false,
      remarks: "",
    };
    try {
      if (locationSummary.websiteUri) {
        missWebSiteLink.isComplete = true;
        missWebSiteLink.remarks = MessageConstants.NoListingsMissing;
      } else {
        missWebSiteLink.isComplete = false;
        missWebSiteLink.remarks = MessageConstants.ListingsMissing;
      }
    } catch (error) {}

    return missWebSiteLink;
  };

  const getMissedAddressLink = () => {
    let missAddressLink = {
      title: MISSING_INFORMATION.Address.title,
      keyIdentifier: MISSING_INFORMATION.Address.key,
      isComplete: false,
      remarks: "",
    };
    try {
      if (locationSummary.websiteUri) {
        missAddressLink.isComplete = true;
        missAddressLink.remarks = MessageConstants.NoListingsMissing;
      } else {
        missAddressLink.isComplete = false;
        missAddressLink.remarks = MessageConstants.ListingsMissing;
      }
    } catch (error) {}

    return missAddressLink;
  };

  const getMissAddCategories = () => {
    let missAddCategories = {
      title: MISSING_INFORMATION.AddCategories.title,
      keyIdentifier: MISSING_INFORMATION.AddCategories.key,
      isComplete: false,
      remarks: "",
    };
    try {
      if (Object.keys(locationSummary.categories).length > 0) {
        missAddCategories.isComplete = true;
        missAddCategories.remarks = MessageConstants.NoListingsMissing;
      } else {
        missAddCategories.isComplete = false;
        missAddCategories.remarks = MessageConstants.ListingsMissing;
      }
    } catch (error) {}

    return missAddCategories;
  };

  const getMissedAttributes = () => {
    let missAttributes = {
      title: MISSING_INFORMATION.Attributes.title,
      keyIdentifier: MISSING_INFORMATION.Attributes.key,
      isComplete: false,
      remarks: "",
    };
    try {
      if (Object.keys(locationSummary.categories).length > 0) {
        missAttributes.isComplete = true;
        missAttributes.remarks = MessageConstants.NoListingsMissing;
      } else {
        missAttributes.isComplete = false;
        missAttributes.remarks = MessageConstants.ListingsMissing;
      }
    } catch (error) {}
    return missAttributes;
  };

  const getMissedMenuLink = () => {
    let missMenuLink = {
      title: MISSING_INFORMATION.MenuLink.title,
      keyIdentifier: MISSING_INFORMATION.MenuLink.key,
      isComplete: false,
      remarks: "",
    };
    try {
      if (locationSummary.menuUrl) {
        missMenuLink.isComplete = true;
        missMenuLink.remarks = MessageConstants.NoListingsMissing;
      } else {
        missMenuLink.isComplete = false;
        missMenuLink.remarks = MessageConstants.ListingsMissing;
      }
    } catch (error) {}
    return missMenuLink;
  };

  const getMissedMenuItems = () => {
    let missMenuItems = {
      title: MISSING_INFORMATION.MenuItems.title,
      keyIdentifier: MISSING_INFORMATION.MenuItems.key,
      isComplete: false,
      remarks: "",
    };
    try {
      if (
        locationSummary.serviceItems &&
        Object.keys(locationSummary.serviceItems).length > 0
      ) {
        missMenuItems.isComplete = true;
        missMenuItems.remarks = MessageConstants.NoListingsMissing;
      } else {
        missMenuItems.isComplete = false;
        missMenuItems.remarks = MessageConstants.ListingsMissing;
      }
    } catch (error) {}

    return missMenuItems;
  };

  const getMissedOpeningDate = () => {
    let missOpeningDate = {
      title: MISSING_INFORMATION.OpeningDate.title,
      keyIdentifier: MISSING_INFORMATION.OpeningDate.key,
      isComplete: false,
      remarks: "",
    };
    try {
      if (
        locationSummary.openInfo &&
        Object.keys(locationSummary.openInfo).length > 0 &&
        locationSummary.openInfo.openingDate
      ) {
        missOpeningDate.isComplete = true;
        missOpeningDate.remarks = MessageConstants.NoListingsMissing;
      } else {
        missOpeningDate.isComplete = false;
        missOpeningDate.remarks = MessageConstants.ListingsMissing;
      }
    } catch (error) {}

    return missOpeningDate;
  };

  const getMissedOpeningHours = () => {
    let missOpeningHours = {
      title: MISSING_INFORMATION.OpeningHours.title,
      keyIdentifier: MISSING_INFORMATION.OpeningHours.key,
      isComplete: false,
      remarks: "",
    };
    try {
      if (
        locationSummary.regularHours &&
        Object.keys(locationSummary.regularHours).length > 0 &&
        locationSummary.regularHours.periods.length > 0
      ) {
        missOpeningHours.isComplete = true;
        missOpeningHours.remarks = MessageConstants.NoListingsMissing;
      } else {
        missOpeningHours.isComplete = false;
        missOpeningHours.remarks = MessageConstants.ListingsMissing;
      }
    } catch (error) {}

    return missOpeningHours;
  };

  const getMissedPhotos = () => {
    let missPhotos = {
      title: MISSING_INFORMATION.Photos.title,
      keyIdentifier: MISSING_INFORMATION.Photos.key,
      isComplete: false,
      remarks: "",
    };
    try {
      if (
        locationSummary.mediaInfo &&
        Object.keys(locationSummary.mediaInfo).length > 0 &&
        locationSummary.mediaInfo.mediaItems &&
        locationSummary.mediaInfo.mediaItems.filter(
          (item: any) => item.mediaFormat === "PHOTO"
        )
      ) {
        missPhotos.isComplete = true;
        missPhotos.remarks = MessageConstants.NoListingsMissing;
      } else {
        missPhotos.isComplete = false;
        missPhotos.remarks = MessageConstants.ListingsMissing;
      }
    } catch (error) {}

    return missPhotos;
  };

  const getMissedVideos = () => {
    let missVideos = {
      title: MISSING_INFORMATION.Videos.title,
      keyIdentifier: MISSING_INFORMATION.Videos.key,
      isComplete: false,
      remarks: "",
    };
    try {
      if (
        locationSummary.mediaInfo &&
        Object.keys(locationSummary.mediaInfo).length > 0 &&
        locationSummary.mediaInfo.mediaItems &&
        locationSummary.mediaInfo.mediaItems.filter(
          (item: any) => item.mediaFormat === "VIDEO"
        )
      ) {
        missVideos.isComplete = true;
        missVideos.remarks = MessageConstants.NoListingsMissing;
      } else {
        missVideos.isComplete = false;
        missVideos.remarks = MessageConstants.ListingsMissing;
      }
    } catch (error) {}

    return missVideos;
  };

  const getMissedBusinessLogo = () => {
    let missBusinessLogo = {
      title: MISSING_INFORMATION.BusinessLogo.title,
      keyIdentifier: MISSING_INFORMATION.BusinessLogo.key,
      isComplete: false,
      remarks: "",
    };
    try {
      if (
        locationSummary.mediaInfo &&
        Object.keys(locationSummary.mediaInfo).length > 0 &&
        locationSummary.mediaInfo.mediaItems &&
        locationSummary.mediaInfo.mediaItems.filter(
          (item: any) => item.locationAssociation?.category === "LOGO"
        ).length > 0
      ) {
        missBusinessLogo.isComplete = true;
        missBusinessLogo.remarks = MessageConstants.NoListingsMissing;
      } else {
        missBusinessLogo.isComplete = false;
        missBusinessLogo.remarks = MessageConstants.ListingsMissing;
      }
    } catch (error) {}

    return missBusinessLogo;
  };

  const getMissedBusinessMenu = () => {
    let missBusinessMenu = {
      title: MISSING_INFORMATION.MenuPhotos.title,
      keyIdentifier: MISSING_INFORMATION.MenuPhotos.key,
      isComplete: false,
      remarks: "",
    };
    try {
      if (
        locationSummary.mediaInfo &&
        Object.keys(locationSummary.mediaInfo).length > 0 &&
        locationSummary.mediaInfo.mediaItems &&
        locationSummary.mediaInfo.mediaItems.filter(
          (item: any) => item.locationAssociation?.category === "MENU"
        ).length > 0
      ) {
        missBusinessMenu.isComplete = true;
        missBusinessMenu.remarks = MessageConstants.NoListingsMissing;
      } else {
        missBusinessMenu.isComplete = false;
        missBusinessMenu.remarks = MessageConstants.ListingsMissing;
      }
    } catch (error) {}

    return missBusinessMenu;
  };

  const getMissedServices = () => {
    let missServices = {
      title: MISSING_INFORMATION.Services.title,
      keyIdentifier: MISSING_INFORMATION.Services.key,
      isComplete: false,
      remarks: "",
    };
    try {
      if (
        locationSummary.categories &&
        locationSummary.categories.primaryCategory &&
        Object.keys(locationSummary.categories.primaryCategory).length > 0 &&
        locationSummary.categories.primaryCategory.serviceTypes &&
        locationSummary.categories.primaryCategory.serviceTypes.length > 0
      ) {
        missServices.isComplete = true;
        missServices.remarks = MessageConstants.NoListingsMissing;
      } else {
        missServices.isComplete = false;
        missServices.remarks = MessageConstants.ListingsMissing;
      }
    } catch (error) {}

    return missServices;
  };

  const getMissedReviewLink = () => {
    let missReviewLink = {
      title: MISSING_INFORMATION.ReviewLink.title,
      keyIdentifier: MISSING_INFORMATION.ReviewLink.key,
      isComplete: false,
      remarks: "",
    };
    try {
      if (locationSummary.metadata && locationSummary.metadata.newReviewUri) {
        missReviewLink.isComplete = true;
        missReviewLink.remarks = MessageConstants.NoListingsMissing;
      } else {
        missReviewLink.isComplete = false;
        missReviewLink.remarks = MessageConstants.ListingsMissing;
      }
    } catch (error) {}

    return missReviewLink;
  };

  // const getMissedWebSiteLink = () => {
  //   try {
  //   } catch (error) {}
  // };

  const performMissingInformationOperation = () => {
    try {
      let missInfo: IMissingInformationContent[] = [];
      /** Phone Number */
      missInfo.push(getMissedPhoneNumber());

      /** Website Link */
      missInfo.push(getMissedWebSiteLink());

      /** Address */
      missInfo.push(getMissedAddressLink());

      /** Add Categories */
      missInfo.push(getMissAddCategories());

      /** Attributes - TO DO */
      missInfo.push(getMissedAttributes());

      /** Menu Link */
      missInfo.push(getMissedMenuLink());

      /** Menu items */
      missInfo.push(getMissedMenuItems());

      /** Opening Date */
      missInfo.push(getMissedOpeningDate());

      /** Opening Hours */
      missInfo.push(getMissedOpeningHours());

      /** Photos */
      missInfo.push(getMissedPhotos());

      /** Videos */
      missInfo.push(getMissedVideos());

      /** Business Logo */
      missInfo.push(getMissedBusinessLogo());

      /** Menu Photos */
      missInfo.push(getMissedBusinessMenu());

      /** Services */
      missInfo.push(getMissedServices());

      /** Review Link */
      missInfo.push(getMissedReviewLink());

      setMissingInformation(missInfo);
    } catch (error) {
    } finally {
    }
  };

  const getPhoneNumber = () => {
    let phoneNumber: string = "";
    if (
      locationSummary &&
      Object.keys(locationSummary.phoneNumbers).length > 0 &&
      locationSummary.phoneNumbers.primaryPhone
    ) {
      phoneNumber = locationSummary.phoneNumbers.primaryPhone;
    }

    return phoneNumber;
  };

  const openInNewTab = (url: string) => {
    window.open(url, "_blank");
  };

  // Phone Number Icon Edit Code
  //  const handleOpenChatModal = () => {
  //   setIsChatModalOpen(true);
  // };

  // const handleCloseChatModal = () => {
  //   setIsChatModalOpen(false);
  // };

  return (
    <div>
      <Box className="businessSummary">
        <LeftMenuComponent>
          <Box className="commonTableHeader">
            <h3 className="commonTitle pageTitle">Profile Summary</h3>
          </Box>

          {/* Top Part */}
          {locationSummary && (
            <Box>
              <Grid container spacing={2} sx={{}}>
                <Grid item xs={12} md={12} lg={12}>
                  <Box>
                    <Box className="marB20">
                      <Card>
                        <CardMedia
                          component={"img"}
                          sx={{
                            height: 300,
                            objectFit: coverPhoto.includes("No_Cover")
                              ? "contain"
                              : "cover",
                          }}
                          image={coverPhoto}
                          title="Cover Photo"
                          alt="MyLocoBiz - Cover Photo"
                        />
                        <CardContent>
                          <Box>
                            <Box>
                              <img
                                alt="MyLocoBiz - Profile Photo"
                                className="businessLogoPreview"
                                src={profilePhoto}
                              />
                            </Box>
                            <Box>
                              <Typography
                                gutterBottom
                                variant="h5"
                                component="div"
                              >
                                {locationSummary.title}
                              </Typography>
                              {locationSummary &&
                                locationSummary.profile &&
                                locationSummary.profile.description && (
                                  <Typography
                                    gutterBottom
                                    variant="subtitle2"
                                    component="div"
                                  >
                                    {locationSummary.profile.description}
                                  </Typography>
                                )}

                              <Typography>
                                <span>{`Category: `}</span>
                                <span>
                                  {
                                    locationSummary?.categories?.primaryCategory
                                      ?.displayName
                                  }
                                </span>
                                <span>
                                  {" "}
                                  <VerifiedRoundedIcon
                                    sx={{
                                      fontSize: 12,
                                      color: "var(--positive)",
                                    }}
                                  />
                                </span>
                              </Typography>
                              <Typography>
                                <span>{`Status: `}</span>
                                <span>
                                  {_applicationHelperService.toTitleCase(
                                    locationSummary?.openInfo?.status
                                  )}
                                </span>
                              </Typography>
                              <Typography
                                variant="body2"
                                sx={{ color: "text.secondary" }}
                              >
                                Address : {getAddress()}
                              </Typography>
                            </Box>
                          </Box>
                        </CardContent>
                      </Card>
                    </Box>
                  </Box>
                </Grid>
                {/* <Grid item xs={12} md={2} lg={2} sx={{ padding: 2 }}>
                  <Box className="sampleBusineesMobile">
                    <img
                      alt="MyLocoBiz - Business Summary"
                      className="width100"
                      src={businessSummaryMobileImage}
                    />
                  </Box>
                </Grid> */}
                <Grid item xs={12}>
                  <Box>
                    <Card>
                      <CardContent>
                        <Box>
                          <Typography gutterBottom variant="h5" component="div">
                            Profile Score
                          </Typography>
                        </Box>
                        <Box>
                          <LinearProgressWithLabel
                            value={calculateProgress()}
                          />
                        </Box>
                        <Box>
                          <Typography>
                            <span>
                              {
                                missingInformation.filter(
                                  (x: IMissingInformationContent) =>
                                    x.isComplete
                                ).length
                              }
                            </span>{" "}
                            Out of
                            <span>
                              {" " + missingInformation.length + " "}{" "}
                            </span>
                            Sections are Incomplete
                          </Typography>
                        </Box>
                      </CardContent>
                    </Card>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          )}

          {/* Missing Information */}
          <Box>
            {missingInformation && missingInformation.length > 0 && (
              <Grid container spacing={2} sx={{ paddingY: 2 }}>
                <Grid item xs={12}>
                  <h4 className="sectionTitle mar0">Profile Status</h4>
                </Grid>
              </Grid>
            )}

            <Box>
              <Grid container spacing={2} sx={{ paddingY: 2 }}>
                {missingInformation &&
                  missingInformation.length > 0 &&
                  missingInformation.map((x: IMissingInformationContent) => (
                    <Grid
                      key={x.keyIdentifier}
                      item
                      xs={12}
                      md={6}
                      lg={3}
                      xl={3}
                      gap={0.5}
                    >
                      <Card className="missingCard">
                        <CardContent
                          style={{ paddingTop: 5, paddingBottom: 5 }}
                        >
                          <Box
                            className={`missingCardInner ${
                              x.isComplete ? "successCard" : "errorCard"
                            }`}
                          >
                            <Box>
                              {/* <Typography>{x.title}</Typography> */}
                              <h4>{x.title}</h4>
                              {/* <Typography className="messageText">
                                {x.remarks}
                              </Typography> */}
                            </Box>
                            <Box>
                              {x.isComplete ? (
                                <Box className="missingStatusCount">
                                  <DoneRoundedIcon
                                    fontSize="small"
                                    color={"success"}
                                  />
                                </Box>
                              ) : (
                                // <Box className="missingStatusCount">1</Box>
                                <Typography className="messageTextMissing">
                                  {x.remarks}
                                </Typography>
                              )}
                            </Box>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
              </Grid>
            </Box>
          </Box>

          {missingInformation && missingInformation.length > 0 && (
            <Box className="whiteBg commonTabs">
              <Box>
                <Tabs
                  className="tabBg"
                  value={selectedTab}
                  onChange={handleChange}
                  variant="scrollable"
                  scrollButtons
                  allowScrollButtonsMobile
                  aria-label="Business Summary"
                  sx={{
                    "& .MuiTabs-indicator": { display: "none" },
                    "& .MuiTab-root": {
                      textTransform: "capitalize",
                      color: "#202630",
                      fontWeight: 600,
                    },
                  }}
                >
                  <Tab
                    label="Basic Business Details"
                    value="basicBusinessDetails"
                  />
                  <Tab label="Categories" value="categories" />
                  <Tab label="Services & FAQ’s" value="servicesandFaqs" />
                  <Tab label="Business Opening" value="businessOpening" />
                  <Tab label="Logo, Photo" value="logoPhoto" />
                  <Tab
                    label="Website, Menu & Appointment"
                    value="websiteMenuAppointments"
                  />
                  {locationSummary && locationSummary.labels && (
                    <Tab label="Labels" value="labels" />
                  )}

                  {locationSummary.serviceArea && (
                    <Tab label="Service Area" value="serviceArea" />
                  )}
                </Tabs>
                <Box sx={{ p: 2 }}>
                  {selectedTab === "basicBusinessDetails" && (
                    <Box>
                      <Grid container spacing={1} sx={{ padding: 1 }}>
                        <Grid
                          item
                          xs={12}
                          md={6}
                          lg={6}
                          xl={4}
                          sx={{ padding: 2 }}
                        >
                          <Card className="businessSummaryTabCard boxShadowNone height100">
                            <CardContent
                              sx={{
                                border: "1px solid #e0e0e0",
                                borderRadius: 2,
                                p: 2,
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                              }}
                            >
                              <Box
                                sx={{
                                  display: "flex",
                                  justifyContent: "space-between",
                                  alignItems: "center",
                                  gap: 5,
                                }}
                              >
                                <Box className="commonTabsIcon">
                                  <LocalPhoneRoundedIcon />
                                </Box>
                                <Box>
                                  <Typography
                                    variant="subtitle1"
                                    fontWeight={600}
                                    className="titleIcon"
                                    sx={{ ml: 1 }}
                                  >
                                    Phone Number
                                  </Typography>
                                  <Typography
                                    variant="body2"
                                    color="text.secondary"
                                    sx={{ ml: 1 }}
                                  >
                                    {getPhoneNumber() ? (
                                      <span
                                        style={{
                                          display: "block",
                                          textAlign: "left",
                                        }}
                                      >
                                        {getPhoneNumber()}
                                      </span>
                                    ) : (
                                      <span>&nbsp;</span>
                                    )}
                                  </Typography>
                                </Box>
                              </Box>
                            </CardContent>
                          </Card>
                        </Grid>

                        <Grid
                          item
                          xs={12}
                          md={6}
                          lg={6}
                          xl={4}
                          sx={{ padding: 2 }}
                        >
                          <Card className="businessSummaryTabCard boxShadowNone height100">
                            <CardContent
                              sx={{
                                border: "1px solid #e0e0e0",
                                borderRadius: 2,
                                p: 2,
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                              }}
                            >
                              <Box
                                sx={{
                                  display: "flex",
                                  justifyContent: "space-between",
                                  alignItems: "center",
                                  gap: 2,
                                }}
                              >
                                <Box className="commonTabsIcon">
                                  <CategoryRoundedIcon />
                                </Box>
                                <Box>
                                  <Typography
                                    variant="subtitle1"
                                    fontWeight={600}
                                    className="titleIcon"
                                  >
                                    Primary & Additional Categories
                                  </Typography>
                                  <Typography
                                    variant="body2"
                                    color="text.secondary"
                                    className=""
                                  >
                                    {/* Add Relevant Categories based on Search
                                    Volume */}
                                    {
                                      locationSummary?.categories
                                        ?.primaryCategory?.displayName
                                    }
                                  </Typography>
                                </Box>
                              </Box>

                              {/* <IconOnAvailability
                                isAvailable={
                                  missingInformation.filter(
                                    (x) =>
                                      x.keyIdentifier ===
                                      MISSING_INFORMATION.AddCategories.key
                                  )[0].isComplete
                                }
                              /> */}
                            </CardContent>
                          </Card>
                        </Grid>

                        <Grid
                          item
                          xs={12}
                          md={6}
                          lg={6}
                          xl={4}
                          sx={{ padding: 2 }}
                        >
                          <Card className="businessSummaryTabCard boxShadowNone height100">
                            <CardContent
                              sx={{
                                border: "1px solid #e0e0e0",
                                borderRadius: 2,
                                p: 2,
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                              }}
                            >
                              <Box
                                sx={{
                                  display: "flex",
                                  justifyContent: "space-between",
                                  alignItems: "center",
                                  gap: 2,
                                }}
                              >
                                <Box className="commonTabsIcon">
                                  <LocationOnRoundedIcon />
                                </Box>
                                <Box>
                                  <Typography
                                    variant="subtitle1"
                                    fontWeight={600}
                                    className="titleIcon"
                                  >
                                    Location Update
                                  </Typography>
                                  <Typography
                                    className=""
                                    variant="body2"
                                    color="text.secondary"
                                  >
                                    {getAddress()}
                                  </Typography>
                                </Box>
                              </Box>

                              {/* <IconOnAvailability
                                isAvailable={
                                  missingInformation.filter(
                                    (x) =>
                                      x.keyIdentifier ===
                                      MISSING_INFORMATION.Address.key
                                  )[0].isComplete
                                }
                              /> */}
                            </CardContent>
                          </Card>
                        </Grid>
                      </Grid>
                    </Box>
                  )}
                  {selectedTab === "categories" && (
                    <CategoryDisplay categories={locationSummary.categories} />
                  )}
                  {selectedTab === "servicesandFaqs" && (
                    <ServiceItemsDisplay
                      serviceItems={locationSummary.serviceItems}
                    />
                  )}
                  {selectedTab === "businessOpening" && (
                    <RegularHoursTable
                      regularHours={locationSummary.regularHours}
                      missingInformation={missingInformation}
                    />
                  )}
                  {selectedTab === "logoPhoto" && (
                    <LogoPhotoSection
                      mediaItems={locationSummary.mediaInfo.mediaItems}
                    />
                  )}
                  {selectedTab === "websiteMenuAppointments" && (
                    <Box>
                      <Grid container spacing={1} sx={{ padding: 1 }}>
                        <Grid item xs={12} md={6} lg={6} sx={{ padding: 2 }}>
                          <Card className="businessSummaryTabCard boxShadowNone">
                            <CardContent
                              sx={{
                                border: "1px solid #e0e0e0",
                                borderRadius: 2,
                                p: 2,
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                              }}
                            >
                              <Box>
                                <Box className="commonTabsIcon">
                                  <LanguageRoundedIcon />
                                </Box>
                                <Typography
                                  variant="subtitle1"
                                  className="titleIcon"
                                  fontWeight={600}
                                >
                                  Website Link
                                </Typography>

                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  <Button
                                    className=""
                                    variant="text"
                                    color="primary"
                                    onClick={() =>
                                      openInNewTab(locationSummary?.websiteUri)
                                    }
                                    sx={{
                                      textTransform: "none",
                                      padding: 0,
                                      minWidth: "auto",
                                      textAlign: "left",
                                    }}
                                  >
                                    {locationSummary?.websiteUri}
                                  </Button>
                                </Typography>
                              </Box>

                              <IconOnAvailability
                                isAvailable={
                                  missingInformation.filter(
                                    (x) =>
                                      x.keyIdentifier ===
                                      MISSING_INFORMATION.WebsiteLink.key
                                  )[0].isComplete
                                }
                              />
                            </CardContent>
                          </Card>
                        </Grid>

                        <Grid item xs={12} md={6} lg={6} sx={{ padding: 2 }}>
                          <Card className="businessSummaryTabCard boxShadowNone">
                            <CardContent
                              sx={{
                                border: "1px solid #e0e0e0",
                                borderRadius: 2,
                                p: 2,
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                              }}
                            >
                              <Box>
                                <Box className="commonTabsIcon">
                                  <EventAvailableRoundedIcon />
                                </Box>
                                <Typography
                                  variant="subtitle1"
                                  className="titleIcon"
                                  fontWeight={600}
                                >
                                  Appointment & Food Ordering Links
                                </Typography>
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                  className=""
                                >
                                  Add your Appointment, Food Delivery Links &
                                  more
                                </Typography>
                              </Box>

                              <IconOnAvailability
                                isAvailable={
                                  missingInformation.filter(
                                    (x) =>
                                      x.keyIdentifier ===
                                      MISSING_INFORMATION.MenuLink.key
                                  )[0].isComplete
                                }
                              />
                            </CardContent>
                          </Card>
                        </Grid>

                        {/* <Grid item xs={12} md={6} lg={6} sx={{ padding: 2 }}>
                          <Card className="businessSummaryTabCard">
                            <CardContent
                              sx={{
                                border: "1px solid #e0e0e0",
                                borderRadius: 2,
                                p: 2,
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                              }}
                            >
                              <Box>
                                <Typography
                                  variant="subtitle1"
                                  className="titleIcon"
                                  fontWeight={600}
                                >
                                  <span>
                                    <CampaignRoundedIcon />
                                  </span>
                                  Social Media Links
                                </Typography>
                                <Typography
                                  className="padL32"
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  Add Logo & Cover Photo in your Profile
                                </Typography>
                              </Box>

                              <CheckCircleIcon
                                sx={{ color: "green", fontSize: 36 }}
                              />
                            </CardContent>
                          </Card>
                        </Grid> */}

                        <Grid item xs={12} md={6} lg={6} sx={{ padding: 2 }}>
                          <Card className="businessSummaryTabCard boxShadowNone">
                            <CardContent
                              sx={{
                                border: "1px solid #e0e0e0",
                                borderRadius: 2,
                                p: 2,
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                              }}
                            >
                              <Box>
                                <Box className="commonTabsIcon">
                                  <ReviewsRoundedIcon />
                                </Box>
                                <Typography
                                  variant="subtitle1"
                                  className="titleIcon"
                                  fontWeight={600}
                                >
                                  Add Review Link
                                </Typography>

                                {missingInformation.filter(
                                  (x) =>
                                    x.keyIdentifier ===
                                    MISSING_INFORMATION.ReviewLink.key
                                )[0].isComplete ? (
                                  <Typography
                                    variant="body2"
                                    color="text.secondary"
                                  >
                                    <Button
                                      className=""
                                      variant="text"
                                      color="primary"
                                      onClick={() =>
                                        openInNewTab(
                                          locationSummary?.metadata
                                            ?.newReviewUri
                                        )
                                      }
                                      sx={{
                                        textTransform: "none",
                                        padding: 0,
                                        minWidth: "auto",
                                        textAlign: "left",
                                      }}
                                    >
                                      {locationSummary?.metadata?.newReviewUri}
                                    </Button>
                                  </Typography>
                                ) : (
                                  <Typography
                                    className="padL32"
                                    variant="body2"
                                    color="text.secondary"
                                  >
                                    Add Review Links for directories to track
                                    Reviews & Ratings
                                  </Typography>
                                )}
                              </Box>

                              <IconOnAvailability
                                isAvailable={
                                  missingInformation.filter(
                                    (x) =>
                                      x.keyIdentifier ===
                                      MISSING_INFORMATION.ReviewLink.key
                                  )[0].isComplete
                                }
                              />
                            </CardContent>
                          </Card>
                        </Grid>

                        <Grid item xs={12} md={6} lg={6} sx={{ padding: 2 }}>
                          <Card className="businessSummaryTabCard boxShadowNone">
                            <CardContent
                              sx={{
                                border: "1px solid #e0e0e0",
                                borderRadius: 2,
                                p: 2,
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "center",
                              }}
                            >
                              <Box>
                                <Box className="commonTabsIcon">
                                  <AddLocationIcon />
                                </Box>
                                <Typography
                                  variant="subtitle1"
                                  className="titleIcon"
                                  fontWeight={600}
                                >
                                  Add Location Coordinates
                                </Typography>

                                {locationSummary && locationSummary.latlng ? (
                                  <Typography
                                    variant="body2"
                                    color="text.secondary"
                                  >
                                    <Button
                                      className=""
                                      variant="text"
                                      color="primary"
                                      onClick={() =>
                                        openInNewTab(
                                          `https://www.google.com/maps/search/?api=1&query=${locationSummary.latlng.latitude},${locationSummary.latlng.longitude}`
                                        )
                                      }
                                      sx={{
                                        textTransform: "none",
                                        padding: 0,
                                        minWidth: "auto",
                                        textAlign: "left",
                                      }}
                                    >
                                      View Maps
                                    </Button>
                                  </Typography>
                                ) : (
                                  <Typography
                                    variant="body2"
                                    color="text.secondary"
                                  >
                                    Add Location coordinates for best navigation
                                    to your location.
                                  </Typography>
                                )}
                              </Box>

                              <IconOnAvailability
                                isAvailable={
                                  locationSummary && locationSummary.latlng
                                }
                              />
                            </CardContent>
                          </Card>
                        </Grid>
                      </Grid>
                    </Box>
                  )}
                  {selectedTab === "labels" && (
                    <div
                      style={{ display: "flex", flexWrap: "wrap", gap: "8px" }}
                    >
                      <Card className="" sx={{ mb: 4 }}>
                        <CardContent>
                          <Accordion expanded className="commonBorderCard">
                            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                              <Typography>Labels</Typography>
                            </AccordionSummary>
                            <AccordionDetails>
                              <div
                                style={{
                                  display: "flex",
                                  flexWrap: "wrap",
                                  gap: "8px",
                                }}
                              >
                                {locationSummary &&
                                  locationSummary.labels &&
                                  locationSummary.labels.map(
                                    (label: any, index: number) => (
                                      <Chip
                                        key={label + index}
                                        label={label}
                                        variant="outlined"
                                      />
                                    )
                                  )}
                              </div>
                            </AccordionDetails>
                          </Accordion>
                        </CardContent>
                      </Card>
                    </div>
                  )}
                  {selectedTab === "serviceArea" && (
                    <ServiceAreaList
                      placeInfos={locationSummary.serviceArea.places.placeInfos}
                    />
                  )}
                </Box>
              </Box>
            </Box>
          )}

          {/* TabsPart */}
        </LeftMenuComponent>
      </Box>
    </div>
  );
};

export default BusinessSummary;
function setIsModalOpen(arg0: boolean) {
  throw new Error("Function not implemented.");
}
