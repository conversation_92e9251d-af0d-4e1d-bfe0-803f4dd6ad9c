import React, { useContext, useEffect } from "react";
import { But<PERSON> } from "@mui/material";
import { useDispatch } from "react-redux";
import FacebookService from "../../services/facebook/facebook.service";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";

interface FacebookLoginProps {
  userId: number;
  onLoginSuccess?: () => void;
}

const FacebookLogin: React.FC<FacebookLoginProps> = ({
  userId,
  onLoginSuccess,
}) => {
  const dispatch = useDispatch();
  const { setToastConfig } = useContext(ToastContext);
  const facebookService = new FacebookService(dispatch);

  // Check for successful authentication on component mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const facebookAuth = urlParams.get("facebook_auth");

    if (facebookAuth === "success") {
      setToastConfig(
        ToastSeverity.Success,
        "Facebook account connected successfully!",
        true
      );

      // Call the success callback to refresh pages
      if (onLoginSuccess) {
        onLoginSuccess();
      }

      // Clean up URL parameters
      const newUrl = window.location.pathname + window.location.hash;
      window.history.replaceState({}, document.title, newUrl);
    } else if (facebookAuth === "error") {
      setToastConfig(
        ToastSeverity.Error,
        "Failed to connect Facebook account",
        true
      );

      // Clean up URL parameters
      const newUrl = window.location.pathname + window.location.hash;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, [onLoginSuccess, setToastConfig]);

  const handleFacebookLogin = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent parent tab click
    try {
      console.log("Facebook Login - userId:", userId);

      if (!userId) {
        setToastConfig(
          ToastSeverity.Error,
          "User ID is required for Facebook authentication",
          true
        );
        return;
      }

      const response = await facebookService.authenticate(userId);

      if (response.success && response.authUrl) {
        // Redirect to Facebook OAuth
        window.location.href = response.authUrl;
      } else {
        setToastConfig(
          ToastSeverity.Error,
          "Failed to initiate Facebook authentication",
          true
        );
      }
    } catch (error: any) {
      console.error("Facebook authentication error:", error);
      setToastConfig(ToastSeverity.Error, "Error connecting to Facebook", true);
    }
  };

  return (
    <Button
      variant="outlined"
      data-facebook-login
      onClick={handleFacebookLogin}
      sx={{
        borderColor: "#1976d2",
        color: "#1976d2",
        minHeight: "50px",
        minWidth: "120px",
        textTransform: "none",
        fontWeight: "bold",
        borderRadius: "4px",
        "&:hover": {
          borderColor: "#1565c0",
          backgroundColor: "rgba(25, 118, 210, 0.04)",
          color: "#1565c0",
        },
        "&:active": {
          borderColor: "#0d47a1",
          backgroundColor: "rgba(25, 118, 210, 0.08)",
          color: "#0d47a1",
        },
        "&:focus": {
          outline: "2px solid #1976d2",
          outlineOffset: "2px",
        },
      }}
    >
      <span className="responsiveHide">Connect</span>
    </Button>
  );
};

export default FacebookLogin;
