// theme.ts
import { createTheme } from "@mui/material/styles";
import { PaletteColor, PaletteOptions, ThemeOptions } from "@mui/material";
import { color } from "html2canvas/dist/types/css/types/color";

declare module "@mui/material/styles" {
  interface Palette {
    primaryAlpha?: PaletteColor;
    secondaryAlpha?: PaletteColor;
    reportColors?: {
      color1: string;
      color2: string;
      color3: string;
      color4: string;
      color5: string;
      color6: string;
      color7: string;
      color8: string;
    };
  }

  interface PaletteOptions {
    primaryAlpha?: PaletteOptions["primary"];
    secondaryAlpha?: PaletteOptions["secondary"];
    reportColors?: {
      color1: string;
      color2: string;
      color3: string;
      color4: string;
      color5: string;
      color6: string;
      color7: string;
      color8: string;
    };
    starRatings?: {
      one: string;
      two: string;
      three: string;
      four: string;
      five: string;
    };
  }
}
const themeOptions: ThemeOptions = {
  // Theme--1
  // palette: {
  //   mode: "light",
  //   primary: {
  //     main: "#00B4E6",
  //         },
  //   secondary: {
  //     main: "#ff6e23",
  //   },
  //   primaryAlpha: {
  //     main: "#00B4E633", // 20% opacity
  //   },
  //   secondaryAlpha: {
  //     main: "#ff6e2333", // 20% opacity
  //   },
  // },

  //  THEME--2
  // palette: {
  //   mode: "light",
  //   primary: {
  //     main: "#00B4E6",
  //         },
  //   secondary: {
  //     main: "#FFC700",
  //   },
  //   primaryAlpha: {
  //     main: "#00B4E633",
  //   },
  //   secondaryAlpha: {
  //     main: "#FFC70033",
  //   },
  // },

  //THEME--3
  // palette: {
  //   mode: "light",
  //   primary: {
  //     main: "#0C2A3F",
  //         },
  //   secondary: {
  //     main: "#8ac539",
  //   },
  //   primaryAlpha: {
  //     main: "#0C2A3F33",
  //   },
  //   secondaryAlpha: {
  //     main: "#FF6E2333",
  //   },
  // },

  // THEME--4
  palette: {
    mode: "light",
    primary: {
      main: "#309898",
    },
    secondary: {
      main: "#F4631E",
    },
    primaryAlpha: {
      main: "#30989833",
    },
    secondaryAlpha: {
      main: "#F4631E33",
    },
    reportColors: {
      color1: "#2E86AB", // Professional Blue - for Impressions
      color2: "#A23B72", // Deep Pink - for Clicks
      color3: "#F18F01", // Vibrant Orange - for Calls
      color4: "#C73E1D", // Strong Red - for Directions
      color5: "#6A994E", // Fresh Green - for Website Clicks
      color6: "#7209B7", // Purple - for Messaging
      color7: "#F77F00", // Bright Orange - for Bookings
      color8: "#003566", // Navy Blue - for additional metrics
    },
    starRatings: {
      one: "#F4A6A6", // Professional Blue - for Impressions
      two: "#F6C8A6", // Deep Pink - for Clicks
      three: "#F6E6A6", // Vibrant Orange - for Calls
      four: "#D1EDA6", // Strong Red - for Directions
      five: "#A6F4A6", // Fresh Green - for Website Clicks
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          maxHeight: 30,
        },
      },
    },
  },
};

export const theme = createTheme({ ...themeOptions });
