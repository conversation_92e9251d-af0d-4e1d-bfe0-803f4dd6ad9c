const axios = require("axios");
const logger = require("../utils/logger");

// Google Places API configuration
const GOOGLE_PLACES_API_KEY = "AIzaSyB8OKKYB2phgpBCJ2UfP6UmxHyTPxJNpCI";
const GOOGLE_PLACES_BASE_URL = "https://maps.googleapis.com/maps/api/place";

/**
 * Get location suggestions for service areas using Google Places API
 */
const getServiceAreaSuggestions = async (req, res) => {
  try {
    const { query } = req.query;

    logger.logControllerAction(
      "googlePlaces",
      "getServiceAreaSuggestions",
      req.requestId,
      {
        query: query,
      }
    );

    // Validate input
    if (!query || query.length < 3) {
      return res.status(400).json({
        success: false,
        message: "Query must be at least 3 characters long",
        data: [],
      });
    }

    logger.info("Fetching service area suggestions from Google Places API", {
      requestId: req.requestId,
      query: query,
    });

    // Call Google Places Autocomplete API
    const response = await axios.get(
      `${GOOGLE_PLACES_BASE_URL}/autocomplete/json`,
      {
        params: {
          input: query,
          key: GOOGLE_PLACES_API_KEY,
          language: "en",
          components: "country:IN", // Restrict to India
          // Don't use 'types: geocode' to allow hotel names and other establishments
        },
        timeout: 5000,
      }
    );

    if (response.data.status === "OK") {
      // Limit to 5 suggestions as requested
      const suggestions = response.data.predictions
        .slice(0, 5)
        .map((prediction) => ({
          placeId: prediction.place_id,
          placeName: prediction.description,
          mainText:
            prediction.structured_formatting?.main_text ||
            prediction.description,
          secondaryText: prediction.structured_formatting?.secondary_text || "",
          types: prediction.types,
        }));

      logger.info("Service area suggestions fetched successfully", {
        requestId: req.requestId,
        query: query,
        suggestionCount: suggestions.length,
      });

      res.status(200).json({
        success: true,
        message: "Service area suggestions fetched successfully",
        data: suggestions,
      });
    } else if (response.data.status === "ZERO_RESULTS") {
      logger.info("No service area suggestions found", {
        requestId: req.requestId,
        query: query,
      });

      res.status(200).json({
        success: true,
        message: "No suggestions found",
        data: [],
      });
    } else {
      logger.error("Google Places API error", {
        requestId: req.requestId,
        query: query,
        status: response.data.status,
        errorMessage: response.data.error_message,
      });

      res.status(500).json({
        success: false,
        message: "Failed to fetch suggestions from Google Places API",
        error: response.data.error_message || "Unknown error",
        data: [],
      });
    }
  } catch (error) {
    logger.error("Error in getServiceAreaSuggestions", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
      query: req.query?.query,
    });

    // Handle specific error types
    if (error.code === "ECONNABORTED") {
      res.status(408).json({
        success: false,
        message: "Request timeout - Google Places API took too long to respond",
        data: [],
      });
    } else if (error.response) {
      res.status(500).json({
        success: false,
        message: "Google Places API error",
        error: error.response.data?.error_message || error.message,
        data: [],
      });
    } else {
      res.status(500).json({
        success: false,
        message: "Internal server error",
        error: error.message,
        data: [],
      });
    }
  }
};

/**
 * Get place details by place ID
 */
const getPlaceDetails = async (req, res) => {
  try {
    const { placeId } = req.params;

    logger.logControllerAction(
      "googlePlaces",
      "getPlaceDetails",
      req.requestId,
      {
        placeId: placeId,
      }
    );

    if (!placeId) {
      return res.status(400).json({
        success: false,
        message: "Place ID is required",
        data: null,
      });
    }

    logger.info("Fetching place details from Google Places API", {
      requestId: req.requestId,
      placeId: placeId,
    });

    // Call Google Places Details API
    const response = await axios.get(`${GOOGLE_PLACES_BASE_URL}/details/json`, {
      params: {
        place_id: placeId,
        key: GOOGLE_PLACES_API_KEY,
        fields:
          "place_id,name,formatted_address,geometry,types,address_components",
        language: "en",
      },
      timeout: 5000,
    });

    if (response.data.status === "OK") {
      const place = response.data.result;
      const placeDetails = {
        placeId: place.place_id,
        placeName: place.name,
        formattedAddress: place.formatted_address,
        geometry: place.geometry,
        types: place.types,
        addressComponents: place.address_components,
      };

      logger.info("Place details fetched successfully", {
        requestId: req.requestId,
        placeId: placeId,
        placeName: place.name,
      });

      res.status(200).json({
        success: true,
        message: "Place details fetched successfully",
        data: placeDetails,
      });
    } else {
      logger.error("Google Places API error for place details", {
        requestId: req.requestId,
        placeId: placeId,
        status: response.data.status,
        errorMessage: response.data.error_message,
      });

      res.status(500).json({
        success: false,
        message: "Failed to fetch place details from Google Places API",
        error: response.data.error_message || "Unknown error",
        data: null,
      });
    }
  } catch (error) {
    logger.error("Error in getPlaceDetails", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
      placeId: req.params?.placeId,
    });

    res.status(500).json({
      success: false,
      message: "Internal server error",
      error: error.message,
      data: null,
    });
  }
};

/**
 * Welcome endpoint for Google Places controller
 */
const welcome = async (req, res) => {
  try {
    logger.logControllerAction("googlePlaces", "welcome", req.requestId);

    const response = {
      message: "Google Places API Controller",
      endpoints: {
        suggestions:
          "/api/google-places/service-area-suggestions?query=<search_term>",
        placeDetails: "/api/google-places/place-details/<place_id>",
      },
    };

    logger.info("Google Places welcome endpoint accessed", {
      requestId: req.requestId,
    });

    res.send(response);
  } catch (error) {
    logger.error("Error in Google Places welcome", {
      requestId: req.requestId,
      error: error.message,
      stack: error.stack,
    });
    res.status(500).json({
      message: "Internal server error",
      error: error.message,
    });
  }
};

module.exports = {
  welcome,
  getServiceAreaSuggestions,
  getPlaceDetails,
};
