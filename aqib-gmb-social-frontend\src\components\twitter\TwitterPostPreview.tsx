import React from "react";
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON>,
} from "@mui/material";
import {
  ChatB<PERSON>bleOutline,
  Repeat,
  FavoriteBorder,
  <PERSON>hare,
  <PERSON><PERSON><PERSON><PERSON>,
  Verified as VerifiedIcon,
} from "@mui/icons-material";
import {
  ITwitterCreatePost,
  ITwitterSelectedAccount,
} from "../../interfaces/request/ITwitterCreatePost";

interface TwitterPostPreviewProps {
  formData: ITwitterCreatePost;
  uploadedImages?: any[];
  selectedAccount?: ITwitterSelectedAccount | null;
  selectedAccounts?: ITwitterSelectedAccount[];
  enableMultiAccountSelection?: boolean;
  showDisconnectedPreview?: boolean;
}

const TwitterPostPreview: React.FC<TwitterPostPreviewProps> = ({
  formData,
  uploadedImages = [],
  selectedAccount,
  selectedAccounts = [],
  enableMultiAccountSelection = false,
  showDisconnectedPreview = false,
}) => {
  // Get the preview account (first selected account or the single selected account)
  const previewAccount = enableMultiAccountSelection
    ? selectedAccounts[0]
    : selectedAccount;

  // Show disconnected preview with placeholder data
  const disconnectedAccount = {
    accountName: "Your Twitter Account",
    profilePicture: "",
    username: "your_username",
    displayName: "Your Name",
  };

  // Get current account for display
  const displayAccount = showDisconnectedPreview
    ? disconnectedAccount
    : previewAccount;

  if (
    !showDisconnectedPreview &&
    !previewAccount &&
    (!selectedAccounts || selectedAccounts.length === 0)
  ) {
    return (
      <Box sx={{ p: 3, textAlign: "center" }}>
        <Typography variant="body2" color="textSecondary">
          Select a Twitter account to see preview
        </Typography>
      </Box>
    );
  }

  const formatText = (text: string) => {
    if (!text) return "";

    // Replace hashtags with styled spans
    let formattedText = text.replace(
      /#(\w+)/g,
      '<span style="color: #1DA1F2; font-weight: 500;">#$1</span>'
    );

    // Replace mentions with styled spans
    formattedText = formattedText.replace(
      /@(\w+)/g,
      '<span style="color: #1DA1F2; font-weight: 500;">@$1</span>'
    );

    // Replace URLs with styled spans
    formattedText = formattedText.replace(
      /(https?:\/\/[^\s]+)/g,
      '<span style="color: #1DA1F2; text-decoration: underline;">$1</span>'
    );

    return formattedText;
  };

  const formatTime = () => {
    const now = new Date();
    return now.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  };

  const formatDate = () => {
    const now = new Date();
    return now.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  return (
    <Box sx={{ maxWidth: 600, mx: "auto" }}>
      {showDisconnectedPreview && (
        <Box
          sx={{
            mb: 2,
            p: 2,
            backgroundColor: "#f9fafb",
            borderRadius: 1,
            border: "1px solid #e0e0e0",
            textAlign: "center",
          }}
        >
          <Typography variant="body2" color="text.secondary">
            Connect your Twitter account to see post preview
          </Typography>
        </Box>
      )}

      <Card
        sx={{
          border: "1px solid #e1e8ed",
          borderRadius: 2,
          overflow: "hidden",
          backgroundColor: "#ffffff",
        }}
      >
        {/* Tweet Header */}
        <Box sx={{ p: 2 }}>
          <Box sx={{ display: "flex", alignItems: "flex-start", mb: 1 }}>
            <Avatar
              src={
                showDisconnectedPreview
                  ? (displayAccount as any)?.profilePicture || ""
                  : previewAccount?.accountPictureUrl ||
                    "/default-twitter-avatar.png"
              }
              sx={{ width: 48, height: 48, mr: 1.5 }}
            />
            <Box sx={{ flex: 1 }}>
              <Box sx={{ display: "flex", alignItems: "center", mb: 0.5 }}>
                <Typography
                  variant="subtitle1"
                  sx={{ fontWeight: 700, color: "#14171a", mr: 0.5 }}
                >
                  {showDisconnectedPreview
                    ? (displayAccount as any)?.displayName || "Your Name"
                    : previewAccount?.accountName || "Your Twitter Account"}
                </Typography>
                {!showDisconnectedPreview && previewAccount?.isVerified && (
                  <VerifiedIcon
                    sx={{ fontSize: 18, color: "#1DA1F2", mr: 0.5 }}
                  />
                )}
                <Typography variant="body2" sx={{ color: "#657786", mr: 0.5 }}>
                  @
                  {showDisconnectedPreview
                    ? (displayAccount as any)?.username || "your_username"
                    : previewAccount?.accountUsername || "username"}
                </Typography>
                <Typography variant="body2" sx={{ color: "#657786" }}>
                  · {formatTime()}
                </Typography>
              </Box>
            </Box>
            <IconButton size="small" sx={{ color: "#657786" }}>
              <MoreHoriz />
            </IconButton>
          </Box>

          {/* Tweet Text */}
          {formData.text && (
            <Typography
              variant="body1"
              sx={{
                color: "#14171a",
                lineHeight: 1.3125,
                fontSize: "15px",
                mb: uploadedImages.length > 0 ? 2 : 1,
                wordBreak: "break-word",
              }}
              dangerouslySetInnerHTML={{
                __html: formatText(formData.text),
              }}
            />
          )}

          {/* Media Preview */}
          {uploadedImages.length > 0 && (
            <Box
              sx={{
                borderRadius: 2,
                overflow: "hidden",
                border: "1px solid #e1e8ed",
                mb: 1,
              }}
            >
              {uploadedImages.length === 1 && (
                <img
                  src={uploadedImages[0].url || uploadedImages[0].preview}
                  alt="Tweet media"
                  style={{
                    width: "100%",
                    maxHeight: "400px",
                    objectFit: "cover",
                    display: "block",
                  }}
                />
              )}
              {uploadedImages.length === 2 && (
                <Box sx={{ display: "flex", gap: 0.25 }}>
                  {uploadedImages.slice(0, 2).map((image, index) => (
                    <img
                      key={index}
                      src={image.url || image.preview}
                      alt={`Tweet media ${index + 1}`}
                      style={{
                        width: "50%",
                        height: "200px",
                        objectFit: "cover",
                        display: "block",
                      }}
                    />
                  ))}
                </Box>
              )}
              {uploadedImages.length >= 3 && (
                <Box
                  sx={{
                    display: "grid",
                    gridTemplateColumns: "1fr 1fr",
                    gap: 0.25,
                  }}
                >
                  <img
                    src={uploadedImages[0].url || uploadedImages[0].preview}
                    alt="Tweet media 1"
                    style={{
                      width: "100%",
                      height: "200px",
                      objectFit: "cover",
                      display: "block",
                      gridRow: "span 2",
                    }}
                  />
                  {uploadedImages.slice(1, 3).map((image, index) => (
                    <img
                      key={index + 1}
                      src={image.url || image.preview}
                      alt={`Tweet media ${index + 2}`}
                      style={{
                        width: "100%",
                        height: "97px",
                        objectFit: "cover",
                        display: "block",
                      }}
                    />
                  ))}
                  {uploadedImages.length > 3 && (
                    <Box
                      sx={{
                        position: "relative",
                        width: "100%",
                        height: "97px",
                        backgroundColor: "rgba(0,0,0,0.5)",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <Typography
                        variant="h6"
                        sx={{ color: "white", fontWeight: 600 }}
                      >
                        +{uploadedImages.length - 3}
                      </Typography>
                    </Box>
                  )}
                </Box>
              )}
            </Box>
          )}

          {/* Tweet Actions */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mt: 1,
              maxWidth: "425px",
            }}
          >
            <IconButton
              size="small"
              sx={{
                color: "#657786",
                "&:hover": {
                  backgroundColor: "rgba(29, 161, 242, 0.1)",
                  color: "#1DA1F2",
                },
              }}
            >
              <ChatBubbleOutline sx={{ fontSize: 18 }} />
            </IconButton>
            <IconButton
              size="small"
              sx={{
                color: "#657786",
                "&:hover": {
                  backgroundColor: "rgba(23, 191, 99, 0.1)",
                  color: "#17bf63",
                },
              }}
            >
              <Repeat sx={{ fontSize: 18 }} />
            </IconButton>
            <IconButton
              size="small"
              sx={{
                color: "#657786",
                "&:hover": {
                  backgroundColor: "rgba(224, 36, 94, 0.1)",
                  color: "#e0245e",
                },
              }}
            >
              <FavoriteBorder sx={{ fontSize: 18 }} />
            </IconButton>
            <IconButton
              size="small"
              sx={{
                color: "#657786",
                "&:hover": {
                  backgroundColor: "rgba(29, 161, 242, 0.1)",
                  color: "#1DA1F2",
                },
              }}
            >
              <Share sx={{ fontSize: 18 }} />
            </IconButton>
          </Box>
        </Box>
      </Card>

      {/* Multi-account indicator */}
      {enableMultiAccountSelection && selectedAccounts.length > 1 && (
        <Box
          sx={{
            p: 2,
            backgroundColor: "#e3f2fd",
            borderRadius: 1,
            border: "1px solid #bbdefb",
            mt: 2,
          }}
        >
          <Typography
            variant="body2"
            sx={{ color: "#1976d2", fontWeight: 600 }}
          >
            🐦 Multi-Account Preview
          </Typography>
          <Typography variant="caption" sx={{ color: "#1565c0" }}>
            This preview shows how the tweet will look for "
            {selectedAccounts[0].accountName}". Similar tweets will be posted to
            all {selectedAccounts.length} selected accounts.
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default TwitterPostPreview;
