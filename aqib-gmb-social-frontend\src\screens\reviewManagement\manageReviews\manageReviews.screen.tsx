import React, {
  FunctionComponent,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import PageProps from "../../../models/PageProps.interface";

//Widgets
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import InputLabel from "@mui/material/InputLabel";
import TextField from "@mui/material/TextField";
import useMediaQuery from "@mui/material/useMediaQuery";
import { useTheme } from "@mui/material/styles";
import { SELECT_DROPDOWN_STYLES } from "../../../constants/styles.constant";

//Icons
import UpdateIcon from "@mui/icons-material/Update";
import HistoryIcon from "@mui/icons-material/History";
import ThumbUpIcon from "@mui/icons-material/ThumbUp";
import ThumbDownIcon from "@mui/icons-material/ThumbDown";
import PostAddIcon from "@mui/icons-material/PostAdd";
import StarBorderIcon from "@mui/icons-material/StarBorder";
import SendIcon from "@mui/icons-material/Send";
import Stack from "@mui/material/Stack";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import Avatar from "@mui/material/Avatar";
import Card from "@mui/material/Card";
import Drawer from "@mui/material/Drawer";
import {
  Divider,
  FormHelperText,
  Grid2,
  SelectChangeEvent,
} from "@mui/material";
import StarRoundedIcon from "@mui/icons-material/StarRounded";
import LocalOfferOutlinedIcon from "@mui/icons-material/LocalOfferOutlined";
import LeftMenuComponent from "../../../components/leftMenu/leftMenu.component";
import "../manageReviews/manageReviews.screen.style.css";
import ReplyReviewsComponent from "../../../components/replyReviews/replyReviews.component";
import CreatePostComponent from "../../../components/createPost/createPost.component";
import CreateTagsComponent from "../../../components/createTags/createTags.component";
import { Formik } from "formik";
import {
  IBusinessGroup,
  IBusinessGroupsResponseModel,
} from "../../../interfaces/response/IBusinessGroupsResponseModel";
import { ILocationListRequestModel } from "../../../interfaces/request/ILocationListRequestModel";
import * as yup from "yup";
import {
  DEFAULT_PAGINATION,
  STARRATINGMAP,
} from "../../../constants/dbConstant.constant";
import { IPaginationModel } from "../../../interfaces/IPaginationModel";
import {
  ILocation,
  ILocationsListResponseModel,
} from "../../../interfaces/response/ILocationsListResponseModel";
import { useDispatch, useSelector } from "react-redux";
import { LoadingContext } from "../../../context/loading.context";
import BusinessService from "../../../services/business/business.service";
import LocationService from "../../../services/location/location.service";
import {
  IBusiness,
  IBusinessListResponseModel,
} from "../../../interfaces/response/IBusinessListResponseModel";
import { IReviewsListRequestModel } from "../../../interfaces/request/IReviewsListRequestModel";
import {
  IReviewsListResponseModel,
  IReviewsResponse,
} from "../../../interfaces/response/IReviewsListResponseModel";
import ReviewService from "../../../services/review/review.service";
import ApplicationHelperService from "../../../services/ApplicationHelperService";
import moment from "moment";
import UserAvatar from "../../../components/userAvatar/userAvatar.component";
import UserAvatarWithName from "../../../components/userAvatarWIthName/userAvatarWIthName.component";
import RatingsStar from "../../../components/ratingsStar/ratingsStar.component";
import GenericDrawer from "../../../components/genericDrawer/genericDrawer.component";
import Chip from "@mui/material/Chip";
import { getIn } from "formik";
import { ToastSeverity } from "../../../constants/toastSeverity.constant";
import { ToastContext } from "../../../context/toast.context";
//Icons
import FilterListIcon from "@mui/icons-material/FilterList";
import SearchOutlinedIcon from "@mui/icons-material/SearchOutlined";
import IconButton from "@mui/material/IconButton";
import { InputAdornment } from "@mui/material";

import SendOutlinedIcon from "@mui/icons-material/SendOutlined";
import { MessageConstants } from "../../../constants/message.constant";
import {
  ITags,
  ITagsResponseModel,
} from "../../../interfaces/response/ITagsResponseModel";
import PostAddOutlinedIcon from "@mui/icons-material/PostAddOutlined";

const ManageReviews: FunctionComponent<PageProps> = ({ title }) => {
  //Responsive Script Handling
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const newestIcon = isMobile ? <UpdateIcon /> : undefined;
  const oldestIcon = isMobile ? <HistoryIcon /> : undefined;
  const highRatingIcon = isMobile ? <ThumbUpIcon /> : undefined;
  const lowRatingIcon = isMobile ? <ThumbDownIcon /> : undefined;
  //Responsive Script Handling
  const dispatch = useDispatch();
  const { userInfo, rbAccess } = useSelector((state: any) => state.authReducer);
  const _businessService = new BusinessService(dispatch);
  const _locationService = new LocationService(dispatch);
  const _reviewService = new ReviewService(dispatch);
  const _applicationHelperService = new ApplicationHelperService({});
  const { setLoading } = useContext(LoadingContext);
  const { setToastConfig, setOpen } = useContext(ToastContext);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [businessGroups, setBusinessGroups] = useState<IBusinessGroup[]>([]);
  const [reviewsList, setReviewsList] = useState<IReviewsResponse[]>([]);
  const [allData, setAllData] = useState<IReviewsResponse[]>([]);
  const [paginationModel, setPaginationModel] =
    useState<IPaginationModel>(DEFAULT_PAGINATION);
  const [locationList, setLocationList] = useState<ILocation[]>([]);
  const [businessList, setBusinessList] = useState<IBusiness[]>([]);
  const [tags, setTags] = useState<ITags[]>([]);
  const [showScroll, setShowScroll] = useState(false);
  const INITIAL_VALUES: IReviewsListRequestModel = {
    businessId: 0,
    businessGroupId: "0",
    locationId: "0",
    orderBy: "gr.createTime DESC",
    rating: "ALL",
    tags: "ALL",
    searchText: "",
  };

  const [initialValues, setInitialValues] =
    useState<IReviewsListRequestModel>(INITIAL_VALUES);
  const [searchText, setSearchText] = useState<string>("");

  const getBusiness = async () => {
    try {
      setLoading(true);
      let roles: IBusinessListResponseModel =
        await _businessService.getBusiness(userInfo.id);
      if (roles.list.length > 0) {
        setBusinessList(roles.list);
      }
    } catch (error) {}

    setLoading(false);
  };

  const getBusinessGroups = async () => {
    try {
      setLoading(true);
      let businessGroups: IBusinessGroupsResponseModel =
        await _businessService.getBusinessGroups(userInfo.id);
      if (businessGroups.data.length > 0) {
        setBusinessGroups(businessGroups.data);
      }
    } catch (error) {}

    setLoading(false);
  };

  const LocationSchema = yup.object().shape({
    businessId: yup
      .number()
      .typeError("Business ID must be a number")
      .moreThan(0, "Business must be selected") // Ensures value is greater than 0
      .required("Business is required"),
    businessGroupId: yup
      .string()
      .nonNullable()
      .required("Account is required")
      .test("len", `Account is required`, (val) => val != "0"),
    locationId: yup
      .string()
      .nonNullable()
      .required("Location is required")
      .test("len", `Location is required`, (val) => val != "0"),
  });

  useEffect(() => {
    getBusiness();
    getBusinessGroups();
    fetchLocations();
    getAllTags();
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScroll(true);
      } else {
        setShowScroll(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const getAllTags = async () => {
    try {
      const allTagsResponse: ITagsResponseModel =
        await _reviewService.getAllTags();

      const filteredTags = allTagsResponse.filter(
        (x) => Boolean(x.isActive) == true
      );

      setTags(filteredTags);
    } catch (error) {
    } finally {
    }
  };

  const fetchLocations = async () => {
    try {
      setLoading(true);
      const locationListResponse: ILocationsListResponseModel =
        await _businessService.getLocations(userInfo.id);
      console.log("Location List: ", locationListResponse.list);
      setLocationList(locationListResponse.list);
    } catch (error) {}

    setLoading(false);
  };

  const fetchReviewsListPaginated = async (
    values: IReviewsListRequestModel
  ) => {
    try {
      setLoading(true);
      const reviewsResponse: IReviewsListResponseModel =
        await _reviewService.getReviewsExtended(
          userInfo.id,
          values.locationId,
          values
        );
      console.log("Users: ", reviewsResponse);
      setReviewsList(reviewsResponse.list);
      setAllData(reviewsResponse.list);
      if (reviewsResponse.list.length === 0) {
        setToastConfig(
          ToastSeverity.Info,
          MessageConstants.NoReviewsFound,
          true
        );
      }
    } catch (error) {}

    setLoading(false);
  };

  const _handleSaveAccount = async (values: IReviewsListRequestModel) => {
    try {
      setLoading(true);
      const isValid = await LocationSchema.isValid(values);
      if (isValid) {
        await fetchReviewsListPaginated(values);
      }
    } catch (error: any) {
    } finally {
      setLoading(false);
    }
  };

  //Dummy Chip Action Implimentation
  const handleClick = () => {
    console.info("You clicked the Chip.");
  };

  const ReviewsListComponent = (props: {
    review: IReviewsResponse;
    index: number;
  }) => {
    const [openReplyMessage, setOpenReplyMessage] = useState<boolean>(false);
    const [openReviewTag, setOpenReviewTag] = useState<boolean>(false);
    const [openCreatePost, setOpenCreatePost] = useState<boolean>(false);

    const onReviewTagsDrawerClose = () => {
      setOpenReviewTag(false);
      if (buttonRef.current) {
        buttonRef.current.click();
      }
    };

    return (
      <Grid2 spacing={2} size={6}>
        <Card variant="outlined" className="commonCard reviewCard">
          <Box>
            <Stack
              direction="row"
              spacing={2}
              justifyContent="space-between"
              className="stackResponsiveColumn"
            >
              <RatingsStar starRating={props.review.starRating} />
              <Box className="stackResponsiveColumnRight">
                <Stack direction="row" spacing={2}>
                  {props.review.review && (
                    <Box>
                      <Button
                        sx={{
                          textTransform: "capitalize",
                        }}
                        className="button-border-radius shadowCase secondaryFillBtn"
                        onClick={() => setOpenCreatePost(true)}
                        variant="contained"
                        startIcon={<PostAddIcon />}
                      >
                        <span className="responsiveHide">Create Post</span>
                      </Button>
                    </Box>
                  )}

                  <Box>
                    <Button
                      sx={{
                        backgroundColor: "var(--fourthColor)",
                        color: "var(--teritoryColor)",
                        textTransform: "capitalize",
                        fontWeight: 600,
                      }}
                      className="button-border-radius shadowCase"
                      onClick={() => setOpenReviewTag(true)}
                      variant="contained"
                      startIcon={<LocalOfferOutlinedIcon />}
                    >
                      <span className="responsiveHide">Add Tag</span>
                    </Button>
                  </Box>
                </Stack>
              </Box>
            </Stack>
          </Box>
          <Box>
            <UserAvatarWithName fullname={props.review.reviewerName} />
          </Box>
          <Box className="commonCardDescription">
            {props.review.review ? (
              <Typography className="reviewContent">
                {props.review.review}
              </Typography>
            ) : (
              <Typography className="reviewContentDefault">
                {
                  "This is a rating review with no additional content provided by the user."
                }
              </Typography>
            )}
          </Box>
          <Grid container spacing={2}>
            <Grid item xs={6} md={9}>
              {/* <Box className="bindedTags">
                {props.review &&
                  props.review.reviewTags &&
                  props.review.reviewTags.split(",").map((tag: string) => (
                    <Box key={tag}>
                      <Chip
                        label={tag}
                        className="tagChips"
                        color={"secondary"} 
                        sx={{
                          border: "1px solid #ccc", 
                          fontWeight: "bold", 
                        }}
                      />
                    </Box>
                  ))}
              </Box> */}
              <Box>
                {Boolean(rbAccess && rbAccess.ReviewsReply) && (
                  <Button
                    className="replyBtn button-border-radius shadowCase"
                    onClick={() => setOpenReplyMessage(true)}
                    variant="contained"
                    endIcon={<SendOutlinedIcon />}
                  >
                    {props.review.reviewReplyComment &&
                    props.review.reviewReplyComment.trim() != ""
                      ? "Replied"
                      : "Reply"}
                  </Button>
                )}
              </Box>
            </Grid>
            <Grid item xs={6} md={3}>
              <Box>
                <Typography className="reviewDate">
                  {_applicationHelperService.getFormatedDate(
                    moment(props.review.createTime, "YYYY-MM-DD").toDate(),
                    "MMM DD, YYYY"
                  )}
                </Typography>
              </Box>
            </Grid>
          </Grid>
          <Box>
            <Stack direction="row">
              <Grid2
                container
                rowSpacing={0.5} // Adds spacing between the items
                sx={{
                  flexWrap: "wrap", // Ensures items wrap to the next row
                }}
              ></Grid2>
            </Stack>
          </Box>
          <Box></Box>
        </Card>

        <GenericDrawer
          component={
            <Box className="height100">
              <ReplyReviewsComponent
                review={props.review}
                closeDrawer={() => setOpenReplyMessage(false)}
              />
            </Box>
          }
          isShow={openReplyMessage}
          callback={() => setOpenReplyMessage(false)}
        />

        <GenericDrawer
          component={
            <Box className="height100">
              <CreateTagsComponent
                review={props.review}
                closeDrawer={() => onReviewTagsDrawerClose()}
              />
            </Box>
          }
          isShow={openReviewTag}
          callback={() => setOpenReviewTag(false)}
        />

        <Drawer
          anchor={"right"}
          open={openCreatePost}
          onClose={() => console.log("Create Post modal closed")}
          sx={{
            "& .MuiDrawer-paper": {
              maxWidth: "90vw", // Set the max width
              width: "100%", // Ensure the drawer does not exceed the max width
            },
            zIndex: (theme) => {
              return theme.zIndex.drawer + theme.zIndex.drawer;
            },
          }}
        >
          <Box className="height100">
            <CreatePostComponent
              review={props.review}
              closeDrawer={() => setOpenCreatePost(false)}
            />
          </Box>
        </Drawer>
      </Grid2>
    );
  };

  const syncReviews = async (values: IReviewsListRequestModel) => {
    try {
      setLoading(true);
      const syncReviewsRequest = {
        "x-gmb-account-id": values.businessGroupId,
        "x-gmb-business-id": values.businessId,
        "x-gmb-location-id": values.locationId,
      };

      const reviewsResponse: IReviewsListResponseModel =
        await _reviewService.refreshReviews(syncReviewsRequest);

      if (reviewsResponse.success) {
        setToastConfig(ToastSeverity.Success, reviewsResponse.message, true);
        if (buttonRef.current) {
          buttonRef.current.click();
        }
      } else {
        setToastConfig(ToastSeverity.Error, reviewsResponse.message, true);
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: any) => {
    const value = e.target.value;
    setSearchText(value);

    if (value.trim() === "") {
      // Show original data if search is cleared
      setReviewsList(allData);
    } else {
      // Filter data
      const filtered = allData.filter((item) =>
        item.review?.toLowerCase().includes(value.toLowerCase())
      );
      setReviewsList(filtered);
    }
  };

  const STAR_RATING_MAP: Record<string, number> = {
    FIVE: 5,
    FOUR: 4,
    THREE: 3,
    TWO: 2,
    ONE: 1,
  };

  return (
    <div>
      <Box>
        <Box>
          <LeftMenuComponent>
            <Box className="bodyPart">
              <Formik
                enableReinitialize
                initialValues={{ ...initialValues }}
                validationSchema={LocationSchema}
                onSubmit={(values, { setSubmitting }) => {
                  _handleSaveAccount(values);
                }}
              >
                {({
                  values,
                  errors,
                  touched,
                  handleChange,
                  handleBlur,
                  handleSubmit,
                  setFieldValue,
                  handleReset,
                  isSubmitting,
                  isValid,
                  /* and other goodies */
                }) => (
                  <form onSubmit={handleSubmit} onReset={handleReset}>
                    <Box className="commonTableHeader">
                      <h3 className="commonTitle pageTitle">
                        Google Reviews & Rating Insights
                      </h3>
                      {/* <FormControl className="commonSelect" fullWidth>
                  <InputLabel id="allTime">All Time</InputLabel>
                  <Select
                    disabled
                    fullWidth
                    value={10}
                    labelId="allTime"
                    id="allTime"
                    label="All Time"
                  >
                    <MenuItem value={10}>All Time</MenuItem>
                  </Select>
                </FormControl> */}
                      {Boolean(rbAccess && rbAccess.ReviewsRefresh) && (
                        <Button
                          onClick={() => syncReviews(values)}
                          className={
                            values.locationId === "0"
                              ? "tableActionBtnDisabled"
                              : "tableActionBtnEnabled"
                          }
                          startIcon={
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              viewBox="0 0 48 48"
                              width="24px"
                              height="24px"
                            >
                              <path
                                fill="#fbc02d"
                                d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12 s5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24s8.955,20,20,20 s20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
                              />
                              <path
                                fill="#e53935"
                                d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039 l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"
                              />
                              <path
                                fill="#4caf50"
                                d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36 c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"
                              />
                              <path
                                fill="#1565c0"
                                d="M43.611,20.083L43.595,20L42,20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571 c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"
                              />
                            </svg>
                          }
                          disabled={values.locationId === "0"}
                        >
                          Sync
                        </Button>
                      )}
                    </Box>
                    <Divider style={{ margin: "10px 0", height: 0 }} />
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={6} lg={3}>
                        <FormControl
                          className="commonSelect"
                          variant="filled"
                          fullWidth
                          error={Boolean(
                            getIn(errors, "businessId") &&
                              getIn(touched, "businessId")
                          )}
                        >
                          <InputLabel id="outlined-country-dropdown-label">
                            Business
                          </InputLabel>

                          <Select
                            fullWidth
                            id="businessId"
                            label="Business"
                            value={values.businessId.toString()}
                            onChange={(evt: SelectChangeEvent) => {
                              setFieldValue("businessGroupId", "0");
                              setFieldValue("locationId", "0");
                              setFieldValue("businessId", +evt.target.value);
                              setPaginationModel({ ...DEFAULT_PAGINATION });
                            }}
                            onBlur={handleBlur}
                            sx={{
                              backgroundColor: "var(--whiteColor)",
                              borderRadius: "5px",
                            }}
                          >
                            <MenuItem value={0}>Select</MenuItem>
                            {businessList &&
                              businessList.map((business: IBusiness) => (
                                <MenuItem
                                  key={business.id}
                                  value={business.id.toString()}
                                >
                                  {business.businessName}
                                </MenuItem>
                              ))}
                          </Select>
                          <FormHelperText>
                            {touched.businessId && errors.businessId
                              ? errors.businessId
                              : ""}
                          </FormHelperText>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} md={6} lg={3}>
                        <FormControl
                          className="commonSelect"
                          variant="filled"
                          fullWidth
                          error={Boolean(
                            getIn(errors, "businessGroupId") &&
                              getIn(touched, "businessGroupId")
                          )}
                        >
                          <InputLabel id="outlined-country-dropdown-label">
                            Account
                          </InputLabel>

                          <Select
                            fullWidth
                            id="businessGroupId"
                            label="Account"
                            value={values.businessGroupId.toString()}
                            onChange={(evt: SelectChangeEvent) => {
                              setFieldValue(
                                "businessGroupId",
                                evt.target.value
                              );
                              setFieldValue("locationId", "0");
                              setPaginationModel({ ...DEFAULT_PAGINATION });
                            }}
                            onBlur={handleBlur}
                            sx={{
                              backgroundColor: "var(--whiteColor)",
                              borderRadius: "5px",
                            }}
                          >
                            <MenuItem value={"0"}>Select</MenuItem>
                            {businessGroups
                              .filter(
                                (x: IBusinessGroup) =>
                                  x.businessId === values.businessId
                              )
                              .map((businessGroup: IBusinessGroup) => (
                                <MenuItem
                                  key={businessGroup.accountId}
                                  value={businessGroup.accountId.toString()}
                                >
                                  {businessGroup.accountName}
                                </MenuItem>
                              ))}
                          </Select>
                          <FormHelperText>
                            {touched.businessGroupId && errors.businessGroupId
                              ? errors.businessGroupId
                              : ""}
                          </FormHelperText>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} md={6} lg={3}>
                        <FormControl
                          className="commonSelect"
                          variant="filled"
                          fullWidth
                          error={Boolean(
                            getIn(errors, "locationId") &&
                              getIn(touched, "locationId")
                          )}
                        >
                          <InputLabel id="outlined-country-dropdown-label">
                            Location
                          </InputLabel>

                          <Select
                            fullWidth
                            id="locationId"
                            label="Location"
                            value={values.locationId.toString()}
                            onChange={(evt: SelectChangeEvent) => {
                              setFieldValue("locationId", evt.target.value);
                              setFieldValue("orderBy", INITIAL_VALUES.orderBy);
                              setFieldValue("rating", INITIAL_VALUES.rating);
                              setFieldValue("tags", INITIAL_VALUES.tags);
                              setFieldValue(
                                "searchText",
                                INITIAL_VALUES.searchText
                              );
                              setPaginationModel({ ...DEFAULT_PAGINATION });
                            }}
                            onBlur={handleBlur}
                            sx={{
                              backgroundColor: "var(--whiteColor)",
                              borderRadius: "5px",
                            }}
                          >
                            <MenuItem value={0}>Select</MenuItem>
                            {locationList
                              .filter(
                                (x: ILocation) =>
                                  x.gmbAccountId === values.businessGroupId
                              )
                              .map((location: ILocation) => (
                                <MenuItem
                                  key={location.gmbLocationId}
                                  value={location.gmbLocationId}
                                >
                                  {location.gmbLocationName}
                                </MenuItem>
                              ))}
                          </Select>
                          <FormHelperText>
                            {touched.locationId && errors.locationId
                              ? errors.locationId
                              : ""}
                          </FormHelperText>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} md={6} lg={3}>
                        <FormControl
                          className="commonSelect"
                          variant="filled"
                          fullWidth
                        >
                          <InputLabel id="outlined-country-dropdown-label">
                            Sort
                          </InputLabel>

                          <Select
                            fullWidth
                            id="locationId"
                            label="Sort"
                            value={values.orderBy.toString()}
                            onChange={(evt: SelectChangeEvent) => {
                              setFieldValue("orderBy", evt.target.value);
                              setPaginationModel({ ...DEFAULT_PAGINATION });
                            }}
                            sx={{
                              backgroundColor: "var(--whiteColor)",
                              borderRadius: "5px",
                            }}
                          >
                            <MenuItem value={"gr.createTime DESC"}>
                              Newest
                            </MenuItem>
                            <MenuItem value={"gr.createTime ASC"}>
                              Oldest
                            </MenuItem>
                            <MenuItem value={"StarRatingInt DESC"}>
                              High Rating
                            </MenuItem>
                            <MenuItem value={"StarRatingInt ASC"}>
                              Low Rating
                            </MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                      {/*<Grid
                        item
                        xs={12}
                        md={6}
                        lg={3}
                        className="commonVerticalCenter"
                      >
                         <Box>
                          <Stack
                            direction="row"
                            spacing={1}
                            className="stackResponsive quickSortChips"
                          >
                            <label className="chipsLabel">
                              <span className="responsiveHide">
                                Quick Sort:
                              </span>
                            </label>
                            <Chip
                              sx={{ padding: "15px" }}
                              className="commonChipBtn"
                              label={isMobile ? "" : "Newest"}
                              onClick={handleClick}
                              icon={newestIcon}
                            />
                            <Chip
                              sx={{ padding: "15px" }}
                              className="commonChipBtn"
                              label={isMobile ? "" : "Oldest"}
                              onClick={handleClick}
                              icon={oldestIcon}
                            />
                            <Chip
                              sx={{ padding: "15px" }}
                              className="commonChipBtn"
                              label={isMobile ? "" : "High Rating"}
                              onClick={handleClick}
                              icon={highRatingIcon}
                            />
                            <Chip
                              sx={{ padding: "15px" }}
                              className="commonChipBtn"
                              label={isMobile ? "" : "Low Rating"}
                              onClick={handleClick}
                              icon={lowRatingIcon}
                            />
                          </Stack>
                        </Box> 
                      </Grid>*/}
                      <Grid item xs={12} md={6} lg={3}>
                        <FormControl className="commonSelect" fullWidth>
                          <InputLabel id="selectRating">
                            Select Rating
                          </InputLabel>
                          <Select
                            fullWidth
                            value={values.rating.toString()}
                            labelId="selectRating"
                            id="selectRating"
                            label="Select Rating"
                            sx={SELECT_DROPDOWN_STYLES}
                            onChange={(evt: SelectChangeEvent) => {
                              setFieldValue("rating", evt.target.value);
                              setPaginationModel({ ...DEFAULT_PAGINATION });
                            }}
                          >
                            <MenuItem value={"ALL"}>
                              <Box display="flex" alignItems="center" gap={1}>
                                <Typography>All Ratings</Typography>
                              </Box>
                            </MenuItem>
                            {Object.keys(STAR_RATING_MAP).map((key) => (
                              <MenuItem key={key} value={key}>
                                <Box
                                  display="flex"
                                  justifyContent="space-between"
                                  alignItems="center"
                                  width="100%"
                                >
                                  <RatingsStar starRating={key} size={20} />

                                  <Typography
                                    fontSize={14}
                                  >{`${STAR_RATING_MAP[key]} Star`}</Typography>
                                </Box>
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} md={6} lg={3}>
                        <FormControl
                          className="commonSelect"
                          variant="filled"
                          fullWidth
                        >
                          <InputLabel id="outlined-country-dropdown-label">
                            Tags
                          </InputLabel>

                          <Select
                            fullWidth
                            id="locationId"
                            label="Tags"
                            value={values.tags.toString()}
                            sx={SELECT_DROPDOWN_STYLES}
                            onChange={(evt: SelectChangeEvent) => {
                              setFieldValue("tags", evt.target.value);
                              setPaginationModel({ ...DEFAULT_PAGINATION });
                            }}
                          >
                            <MenuItem value={"ALL"}>All</MenuItem>
                            {tags.map((tag: ITags, index: number) => (
                              <MenuItem key={index} value={tag.tagName}>
                                {tag.tagName}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      </Grid>
                      <Grid item xs={12} md={6} lg={3}>
                        <Box className="">
                          <TextField
                            id="search"
                            className="width100  text-box-border-radius"
                            label="Search Reviews"
                            type="text"
                            variant="filled"
                            value={values.searchText}
                            onChange={(evt: any) => {
                              setFieldValue("searchText", evt.target.value);
                              setPaginationModel({ ...DEFAULT_PAGINATION });
                            }}
                            sx={{
                              backgroundColor: "#ffffff",
                              "& .MuiFilledInput-root": {
                                backgroundColor: "#ffffff",
                                borderRadius: "12px",
                                "&:hover": {
                                  backgroundColor: "#ffffff",
                                },
                                "&.Mui-focused": {
                                  backgroundColor: "#ffffff",
                                },
                              },
                            }}
                          />
                        </Box>
                      </Grid>
                      <Grid item xs={12} md={6} lg={3}>
                        <Button
                          className="commonShapeBtn"
                          ref={buttonRef}
                          variant="contained"
                          fullWidth
                          type="submit"
                          startIcon={<FilterListIcon />}
                        >
                          <span className="responsiveHide">Apply Filter</span>
                        </Button>
                      </Grid>
                    </Grid>
                  </form>
                )}
              </Formik>

              <Box className="marT30">
                <Box>
                  {/* <Grid2 container spacing={2}>
                    {reviewsList.length > 0 &&
                      reviewsList.map(
                        (review: IReviewsResponse, index: number) => (
                          <Grid item xs={12} md={6} lg={6} key={index}>
                            <ReviewsListComponent review={review} index={index} />
                          </Grid>
                        )
                      )}
                  </Grid2> */}
                  <Grid container spacing={2}>
                    {reviewsList.length > 0 ? (
                      reviewsList.map(
                        (review: IReviewsResponse, index: number) => (
                          <Grid item xs={12} md={6} lg={6} key={index}>
                            <ReviewsListComponent
                              review={review}
                              index={index}
                            />
                          </Grid>
                        )
                      )
                    ) : (
                      <Grid item xs={12}>
                        <Box
                          display="flex"
                          justifyContent="center"
                          alignItems="center"
                          flexDirection="row"
                          py={6}
                          gap={2}
                        >
                          <PostAddOutlinedIcon
                            style={{ fontSize: 50, color: "#ccc" }}
                          />
                          <Typography variant="h6" color="textSecondary">
                            No Reviews Found
                          </Typography>
                        </Box>
                      </Grid>
                    )}
                  </Grid>
                </Box>
              </Box>
            </Box>
          </LeftMenuComponent>
        </Box>
      </Box>
      {/* {showScroll && (
        <Button
          onClick={scrollToTop}
          variant="contained"
          sx={{
            position: "fixed",
            bottom: 30,
            right: 30,
            zIndex: 9999,
            borderRadius: "50%",
            minWidth: 50,
            minHeight: 50,
            padding: 0,
          }}
        >
          ↑
        </Button>
      )} */}
    </div>
  );
};

export default ManageReviews;
