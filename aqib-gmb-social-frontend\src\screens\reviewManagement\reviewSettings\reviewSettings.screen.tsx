import React, { useContext, useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Card,
  CardContent,
  Grid,
  Tabs,
  Tab,
  Button,
  IconButton,
  Chip,
  Stack,
  Rating,
  Switch,
  FormControlLabel,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Divider,
} from "@mui/material";
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Settings as SettingsIcon,
  Star as StarIcon,
  Schedule as ScheduleIcon,
} from "@mui/icons-material";
import { useDispatch, useSelector } from "react-redux";
import { LoadingContext } from "../../../context/loading.context";
import { ToastContext } from "../../../context/toast.context";
import { ToastSeverity } from "../../../constants/toastSeverity.constant";
import { MessageConstants } from "../../../constants/message.constant";
import LeftMenuComponent from "../../../components/leftMenu/leftMenu.component";
import ReviewSettingsService, {
  IReplyTemplate,
  IAutoReplySettings,
  ICreateReplyTemplateRequest,
} from "../../../services/reviewSettings/reviewSettings.service";
import BusinessService from "../../../services/business/business.service";
import { IBusiness } from "../../../interfaces/response/IBusinessListResponseModel";
import { IBusinessGroup } from "../../../interfaces/response/IBusinessGroupsResponseModel";
import { ILocation } from "../../../interfaces/response/ILocationsListResponseModel";
import GenericDrawer from "../../../components/genericDrawer/genericDrawer.component";
import CreateEditTemplateComponent from "./components/createEditTemplate.component";
import AutoReplySettingsComponent from "./components/autoReplySettings.component";
import ConfirmModel from "../../../components/confirmModel/confirmModel.component";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`review-settings-tabpanel-${index}`}
      aria-labelledby={`review-settings-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `review-settings-tab-${index}`,
    "aria-controls": `review-settings-tabpanel-${index}`,
  };
}

interface IReviewSettingsScreenProps {
  title: string;
}

const ReviewSettingsScreen: React.FunctionComponent<
  IReviewSettingsScreenProps
> = (props) => {
  const dispatch = useDispatch();
  const { setLoading } = useContext(LoadingContext);
  const { setToastConfig } = useContext(ToastContext);
  const { userInfo } = useSelector((state: any) => state.authReducer);

  const [tabValue, setTabValue] = useState(0);
  const [templates, setTemplates] = useState<IReplyTemplate[]>([]);
  const [businesses, setBusinesses] = useState<IBusiness[]>([]);
  const [businessGroups, setBusinessGroups] = useState<IBusinessGroup[]>([]);
  const [locationList, setLocationList] = useState<ILocation[]>([]);
  const [selectedBusiness, setSelectedBusiness] = useState<number | null>(null);
  const [selectedAccount, setSelectedAccount] = useState<string | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<string | null>(null);
  const [autoReplySettings, setAutoReplySettings] =
    useState<IAutoReplySettings | null>(null);

  // Drawer states
  const [openTemplateDrawer, setOpenTemplateDrawer] = useState(false);
  const [openAutoReplyDrawer, setOpenAutoReplyDrawer] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<IReplyTemplate | null>(
    null
  );

  // Delete confirmation state
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [templateToDelete, setTemplateToDelete] = useState<number | null>(null);

  const _reviewSettingsService = new ReviewSettingsService(dispatch);
  const _businessService = new BusinessService(dispatch);

  useEffect(() => {
    if (userInfo?.id) {
      loadBusinesses();
      loadBusinessGroups();
      loadLocations();
    }
  }, [userInfo]);

  useEffect(() => {
    console.log("=== REVIEW SETTINGS DEBUG ===");
    console.log("Selected Business:", selectedBusiness);
    console.log("Selected Account:", selectedAccount);
    console.log("Selected Location:", selectedLocation);
    console.log("User Info:", userInfo?.id);

    if (
      selectedBusiness &&
      selectedAccount &&
      selectedLocation &&
      userInfo?.id
    ) {
      console.log("Loading templates and auto-reply settings...");
      loadTemplates();
      loadAutoReplySettings();
    }
  }, [selectedBusiness, selectedAccount, selectedLocation, userInfo]);

  const loadBusinesses = async () => {
    if (!userInfo?.id) {
      console.warn("User not authenticated");
      return;
    }

    try {
      setLoading(true);
      const response = await _businessService.getBusiness(userInfo.id);
      if (response.list && response.list.length > 0) {
        setBusinesses(response.list);
        setSelectedBusiness(response.list[0].id);
      }
    } catch (error) {
      setToastConfig(
        ToastSeverity.Error,
        MessageConstants.ApiErrorStandardMessage,
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const loadBusinessGroups = async () => {
    if (!userInfo?.id) {
      console.warn("User not authenticated");
      return;
    }

    try {
      setLoading(true);
      console.log("Loading business groups for user:", userInfo.id);
      const response = await _businessService.getBusinessGroups(userInfo.id);
      console.log("Business groups response:", response);
      if (response.data && response.data.length > 0) {
        setBusinessGroups(response.data);
        console.log("Business groups loaded:", response.data.length);
      } else {
        console.log("No business groups found");
      }
    } catch (error) {
      console.error("Error loading business groups:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Failed to load business accounts",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const loadLocations = async () => {
    if (!userInfo?.id) {
      console.warn("User not authenticated");
      return;
    }

    try {
      setLoading(true);
      const response = await _businessService.getLocations(userInfo.id);
      if (response.list && response.list.length > 0) {
        setLocationList(response.list);
      }
    } catch (error) {
      setToastConfig(ToastSeverity.Error, "Failed to load locations", true);
    } finally {
      setLoading(false);
    }
  };

  const loadTemplates = async () => {
    if (!userInfo?.id) {
      console.warn("User not authenticated");
      return;
    }

    try {
      setLoading(true);
      const response = await _reviewSettingsService.getReplyTemplates(
        userInfo.id,
        selectedBusiness || undefined,
        selectedAccount || undefined,
        selectedLocation || undefined
      );
      setTemplates(response.data || []);
    } catch (error) {
      setToastConfig(
        ToastSeverity.Error,
        "Failed to load reply templates",
        true
      );
    } finally {
      setLoading(false);
    }
  };

  const loadAutoReplySettings = async () => {
    try {
      if (!selectedBusiness) return;

      console.log("=== LOADING AUTO-REPLY SETTINGS ===");
      console.log("Business ID:", selectedBusiness);
      console.log("Account ID:", selectedAccount);
      console.log("Location ID:", selectedLocation);

      const response = await _reviewSettingsService.getAutoReplySettings(
        selectedBusiness,
        selectedAccount || undefined,
        selectedLocation || undefined
      );

      console.log("Auto-reply settings response:", response);
      setAutoReplySettings(response.data);
    } catch (error) {
      console.error("Error loading auto-reply settings:", error);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleBusinessChange = (businessId: number) => {
    setSelectedBusiness(businessId);
  };

  const handleCreateTemplate = () => {
    setEditingTemplate(null);
    setOpenTemplateDrawer(true);
  };

  const handleEditTemplate = (template: IReplyTemplate) => {
    setEditingTemplate(template);
    setOpenTemplateDrawer(true);
  };

  const handleDeleteTemplate = (templateId: number) => {
    if (!userInfo?.id) {
      setToastConfig(ToastSeverity.Error, "User not authenticated", true);
      return;
    }

    setTemplateToDelete(templateId);
    setShowDeleteConfirm(true);
  };

  const confirmDeleteTemplate = async () => {
    if (!userInfo?.id || !templateToDelete) {
      return;
    }

    try {
      setLoading(true);
      setShowDeleteConfirm(false);

      await _reviewSettingsService.deleteReplyTemplate(
        userInfo.id,
        templateToDelete
      );
      setToastConfig(
        ToastSeverity.Success,
        "Template deleted successfully",
        true
      );
      loadTemplates();
    } catch (error) {
      setToastConfig(ToastSeverity.Error, "Failed to delete template", true);
    } finally {
      setLoading(false);
      setTemplateToDelete(null);
    }
  };

  const cancelDeleteTemplate = () => {
    setShowDeleteConfirm(false);
    setTemplateToDelete(null);
  };

  const handleTemplateDrawerClose = () => {
    setOpenTemplateDrawer(false);
    setEditingTemplate(null);
    loadTemplates();
  };

  const handleAutoReplyDrawerClose = () => {
    setOpenAutoReplyDrawer(false);
    loadAutoReplySettings();
  };

  const getStarRatingColor = (rating: number) => {
    const colors = {
      1: "#f44336", // red
      2: "#ff9800", // orange
      3: "#ffc107", // amber
      4: "#4caf50", // green
      5: "#2196f3", // blue
    };
    return colors[rating as keyof typeof colors] || "#757575";
  };

  const groupTemplatesByRating = (templates: IReplyTemplate[]) => {
    const grouped: { [key: number]: IReplyTemplate[] } = {};
    templates.forEach((template) => {
      if (!grouped[template.star_rating]) {
        grouped[template.star_rating] = [];
      }
      grouped[template.star_rating].push(template);
    });
    return grouped;
  };

  const groupedTemplates = groupTemplatesByRating(templates);

  // Show loading or authentication message if user is not authenticated
  if (!userInfo?.id) {
    return (
      <LeftMenuComponent>
        <Box>
          <Box sx={{ marginBottom: "5px" }}>
            <h3 className="pageTitle">Review Settings</h3>
            <Typography variant="subtitle2" className="subtitle2">
              Manage reply templates and auto-reply settings for your reviews
            </Typography>
          </Box>
          <Box sx={{ mt: 3 }}>
            <Typography variant="body1" color="text.secondary">
              Please log in to access Review Settings.
            </Typography>
          </Box>
        </Box>
      </LeftMenuComponent>
    );
  }

  return (
    <LeftMenuComponent>
      <Box>
        <Box sx={{ marginBottom: "5px" }}>
          <h3 className="pageTitle">Review Settings</h3>
          <Typography variant="subtitle2" className="subtitle2">
            Manage reply templates and auto-reply settings for your reviews
          </Typography>
        </Box>
        <Divider sx={{ mb: 2 }}></Divider>
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Select Business</InputLabel>
                <Select
                  value={selectedBusiness || ""}
                  label="Select Business"
                  onChange={(e) => {
                    const businessId = Number(e.target.value);
                    setSelectedBusiness(businessId);
                    setSelectedAccount(null);
                    setSelectedLocation(null);
                  }}
                  sx={{
                    backgroundColor: "var(--whiteColor)",
                    borderRadius: "5px",
                  }}
                >
                  {businesses.map((business) => (
                    <MenuItem key={business.id} value={business.id}>
                      {business.businessName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl fullWidth disabled={!selectedBusiness}>
                <InputLabel>Select Account</InputLabel>
                <Select
                  value={selectedAccount || ""}
                  label="Select Account"
                  onChange={(e) => {
                    setSelectedAccount(e.target.value);
                    setSelectedLocation(null);
                  }}
                  sx={{
                    backgroundColor: "var(--whiteColor)",
                    borderRadius: "5px",
                  }}
                >
                  {businessGroups
                    .filter((group) => group.businessId === selectedBusiness)
                    .map((group) => (
                      <MenuItem key={group.accountId} value={group.accountId}>
                        {group.accountName}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl fullWidth disabled={!selectedAccount}>
                <InputLabel>Select Location</InputLabel>
                <Select
                  value={selectedLocation || ""}
                  label="Select Location"
                  onChange={(e) => setSelectedLocation(e.target.value)}
                  sx={{
                    backgroundColor: "var(--whiteColor)",
                    borderRadius: "5px",
                  }}
                >
                  {locationList
                    .filter(
                      (location) => location.gmbAccountId === selectedAccount
                    )
                    .map((location) => (
                      <MenuItem
                        key={location.gmbLocationId}
                        value={location.gmbLocationId}
                      >
                        {location.gmbLocationName}
                      </MenuItem>
                    ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Box>

        {selectedBusiness && selectedAccount && selectedLocation && (
          <Card>
            <CardContent>
              <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
                <Tabs
                  value={tabValue}
                  onChange={handleTabChange}
                  aria-label="review settings tabs"
                >
                  <Tab
                    sx={{ textTransform: "none" }}
                    label="Reply Templates"
                    {...a11yProps(0)}
                  />
                  <Tab
                    sx={{ textTransform: "none" }}
                    label="Auto-Reply Settings"
                    {...a11yProps(1)}
                  />
                </Tabs>
              </Box>

              <TabPanel value={tabValue} index={0}>
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    mb: 3,
                  }}
                >
                  <Typography variant="h6"></Typography>
                  <Button
                    variant="contained"
                    onClick={handleCreateTemplate}
                    sx={{
                      minHeight: "50px",
                    }}
                    startIcon={
                      <AddIcon sx={{ display: "block", marginRight: "-8px" }} />
                    }
                  >
                    <span className="responsiveHide">Create Template</span>
                  </Button>
                </Box>

                <Grid container spacing={3}>
                  {[1, 2, 3, 4, 5].map((rating) => (
                    <Grid item xs={12} key={rating}>
                      <Card variant="outlined">
                        <CardContent>
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "start",
                              mb: 2,
                            }}
                          >
                            <Rating value={rating} readOnly size="small" />
                            <Typography variant="h6" sx={{ ml: 1 }}>
                              {rating} Star Templates
                            </Typography>
                            <Chip
                              label={`${
                                groupedTemplates[rating]?.length || 0
                              } templates`}
                              size="small"
                              sx={{ ml: "auto" }}
                            />
                          </Box>

                          {groupedTemplates[rating]?.length > 0 ? (
                            <Stack spacing={2}>
                              {groupedTemplates[rating].map((template) => (
                                <Card
                                  key={template.id}
                                  sx={{ p: 2, boxShadow: 0 }}
                                >
                                  <Box
                                    sx={{
                                      display: "flex",
                                      justifyContent: "space-between",
                                      alignItems: "flex-start",
                                    }}
                                  >
                                    <Box sx={{ flex: 1 }}>
                                      <Typography
                                        variant="subtitle1"
                                        fontWeight="bold"
                                      >
                                        {template.template_name}
                                        {Boolean(template.is_default) && (
                                          <Chip
                                            label="Default"
                                            size="small"
                                            color="primary"
                                            sx={{ ml: 1 }}
                                          />
                                        )}
                                      </Typography>
                                      <Typography
                                        variant="body2"
                                        color="text.secondary"
                                        sx={{ mt: 1 }}
                                      >
                                        {template.template_content}
                                      </Typography>
                                    </Box>
                                    <Box>
                                      <IconButton
                                        size="small"
                                        onClick={() =>
                                          handleEditTemplate(template)
                                        }
                                      >
                                        <EditIcon />
                                      </IconButton>
                                      <IconButton
                                        size="small"
                                        onClick={() =>
                                          handleDeleteTemplate(template.id!)
                                        }
                                        color="error"
                                      >
                                        <DeleteIcon />
                                      </IconButton>
                                    </Box>
                                  </Box>
                                  <Divider sx={{ mt: 2 }} />
                                </Card>
                              ))}
                            </Stack>
                          ) : (
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{ fontStyle: "italic" }}
                            >
                              No templates created for {rating} star reviews
                              yet.
                            </Typography>
                          )}
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </TabPanel>

              <TabPanel value={tabValue} index={1}>
                <AutoReplySettingsComponent
                  businessId={selectedBusiness}
                  accountId={selectedAccount || undefined}
                  locationId={selectedLocation || undefined}
                  settings={autoReplySettings}
                  onSettingsUpdate={loadAutoReplySettings}
                />
              </TabPanel>
            </CardContent>
          </Card>
        )}

        {/* Template Create/Edit Drawer */}
        <GenericDrawer
          component={
            <CreateEditTemplateComponent
              template={editingTemplate}
              businessId={selectedBusiness}
              accountId={selectedAccount}
              locationId={selectedLocation}
              onClose={handleTemplateDrawerClose}
            />
          }
          isShow={openTemplateDrawer}
          callback={() => setOpenTemplateDrawer(false)}
        />

        {/* Delete Confirmation Modal */}
        <ConfirmModel
          isOpen={showDeleteConfirm}
          title="Delete Template"
          description="Are you certain you want to delete this template? This action is irreversible."
          confirmText="Delete"
          cancelText="Cancel"
          cancelCallback={cancelDeleteTemplate}
          confirmCallback={confirmDeleteTemplate}
        />
      </Box>
    </LeftMenuComponent>
  );
};

export default ReviewSettingsScreen;
