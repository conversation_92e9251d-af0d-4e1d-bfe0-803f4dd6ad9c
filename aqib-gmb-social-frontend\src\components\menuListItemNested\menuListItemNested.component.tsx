import { FunctionComponent, useContext } from "react";
import { cloneElement } from "react";

//Widgets
import List from "@mui/material/List";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemIcon from "@mui/material/ListItemIcon";
import ListItemText from "@mui/material/ListItemText";
import { Box, Collapse, Tooltip, Badge } from "@mui/material";
import useMediaQuery from "@mui/material/useMediaQuery";
// import Accordion from '@material-ui/core/Accordion';
// import AccordionSummary from '@material-ui/core/AccordionSummary';
// import AccordionDetails from '@material-ui/core/AccordionDetails';
// import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import { ExpandLess, ExpandMore } from "@mui/icons-material";
import { NestedMenuItems } from "../../interfaces/nestedMenuItems";
import { useLocation, useNavigate } from "react-router-dom";
import { PreferencesContext } from "../../context/preferences.context";
import React from "react";
import { SvgIconProps } from "@mui/material/SvgIcon";
import { useDispatch } from "react-redux";
import { openMenu } from "../../actions/userPreferences.actions";

const MenuListItemNestedComponent: FunctionComponent<{
  props: NestedMenuItems;
}> = ({ props }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { setActiveMenuItem } = useContext(PreferencesContext);
  const dispatch = useDispatch();
  const isMobile = useMediaQuery("(max-width:600px)");

  const closeDrawerOnMobile = () => {
    if (isMobile) {
      closeDrawer();
    }
  };

  const closeDrawer = () => {
    dispatch<any>(openMenu(false));
  };

  return (
    <div>
      <ListItemButton
        className={location.pathname.includes(props.id) ? "selectedMenu" : ""}
        key={props.title + "0"}
        sx={[
          {
            minHeight: 48,
            px: 2.5,
          },
          props.open
            ? {
                justifyContent: "initial",
              }
            : {
                justifyContent: "center",
              },
        ]}
        onClick={() => {
          props.onToggle?.(); // <-- collapse/expand logic
          if (props.navigateTo) {
            setActiveMenuItem(props.navigateTo);
            navigate(props.navigateTo);
          }
        }}
      >
        <Tooltip
          title={
            !props.open
              ? `${props.title}${
                  props.notificationCount && props.notificationCount > 0
                    ? ` (${props.notificationCount})`
                    : ""
                }`
              : ""
          }
          placement="right"
        >
          <ListItemIcon
            sx={{
              minWidth: 0,
              justifyContent: "center",
              mr: props.open ? 3 : "auto",
              position: "relative",
            }}
            className={
              location.pathname.includes(props.id) ? "selectedIcon" : ""
            }
          >
            {(() => {
              const shouldShowCollapsedBadge =
                !props.open &&
                props.notificationCount &&
                props.notificationCount > 0;

              return shouldShowCollapsedBadge ? (
                <Badge
                  badgeContent={props.notificationCount}
                  color="error"
                  sx={{
                    "& .MuiBadge-badge": {
                      fontSize: "0.65rem",
                      minWidth: "16px",
                      height: "16px",
                      top: -2,
                      right: -2,
                    },
                  }}
                >
                  {cloneElement(
                    props.icon as React.ReactElement<SvgIconProps>,
                    {
                      sx: {
                        color: location.pathname.includes(props.id)
                          ? "var(--secondaryColor)"
                          : "inherit",
                      },
                    }
                  )}
                </Badge>
              ) : (
                cloneElement(props.icon as React.ReactElement<SvgIconProps>, {
                  sx: {
                    color: location.pathname.includes(props.id)
                      ? "var(--secondaryColor)"
                      : "inherit",
                  },
                })
              );
            })()}
          </ListItemIcon>
        </Tooltip>

        <ListItemText
          sx={{ opacity: props.open ? 1 : 0, transition: "opacity 0.3s" }}
          primary={
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              {props.title}
              {(() => {
                const shouldShowBadge =
                  props.notificationCount && props.notificationCount > 0;

                return shouldShowBadge ? (
                  <Badge
                    badgeContent={props.notificationCount}
                    color="error"
                    style={{ paddingRight: "10px" }}
                    sx={{
                      "& .MuiBadge-badge": {
                        fontSize: "0.75rem",
                        minWidth: "18px",
                        height: "18px",
                      },
                    }}
                  />
                ) : null;
              })()}
            </Box>
          }
          slotProps={{
            primary: {
              sx: { fontWeight: 600 },
            },
          }}
        />
        {props.nested != null && props.nested.length > 0 && props.open ? (
          props.isToggled ? (
            <ExpandLess />
          ) : (
            <ExpandMore />
          )
        ) : (
          <></>
        )}
      </ListItemButton>
      {props.nested != null &&
        props.nested.length > 0 &&
        props.nested.map(
          (nestedMenu: NestedMenuItems, index: number) =>
            nestedMenu.isAccessible && (
              <Collapse
                in={props.isToggled}
                timeout="auto"
                unmountOnExit
                key={nestedMenu.title + "Collapse" + index}
              >
                <List
                  component="div"
                  disablePadding
                  key={nestedMenu.title + "CollapseDiv" + index}
                >
                  <ListItemButton
                    sx={{ pl: 4 }}
                    onClick={() => {
                      if (nestedMenu.navigateTo) {
                        setActiveMenuItem(nestedMenu.navigateTo);
                        navigate(nestedMenu.navigateTo);
                      }
                    }}
                    // className={
                    //   nestedMenu.navigateTo === location.pathname
                    //     ? "selectedMenu"
                    //     : ""
                    // }
                  >
                    {nestedMenu.icon && (
                      <Tooltip
                        title={!props.open ? props.title : ""}
                        placement="right"
                      >
                        <ListItemIcon
                          sx={{
                            minWidth: 0,
                            justifyContent: "center",
                            mr: props.open ? 3 : "auto",
                          }}
                        >
                          {cloneElement(
                            nestedMenu.icon as React.ReactElement<SvgIconProps>,
                            {
                              sx: {
                                color: location.pathname.includes(
                                  nestedMenu.navigateTo
                                )
                                  ? "var(--secondaryColor)"
                                  : "inherit",
                              },
                            }
                          )}
                        </ListItemIcon>
                      </Tooltip>
                    )}

                    <ListItemText
                      sx={{
                        opacity: props.open ? 1 : 0,
                        transition: "opacity 0.3s",
                        color: location.pathname.includes(nestedMenu.navigateTo)
                          ? "var(--secondaryColor)"
                          : "inherit",
                      }}
                      slotProps={{
                        primary: {
                          sx: { fontWeight: 600 },
                        },
                      }}
                      primary={
                        <Box
                          sx={{ display: "flex", alignItems: "center", gap: 1 }}
                        >
                          {nestedMenu.title}
                          {nestedMenu.notificationCount &&
                            nestedMenu.notificationCount > 0 && (
                              <Badge
                                badgeContent={nestedMenu.notificationCount}
                                color="error"
                                style={{ paddingRight: "10px" }}
                                sx={{
                                  "& .MuiBadge-badge": {
                                    fontSize: "0.65rem",
                                    minWidth: "16px",
                                    height: "16px",
                                  },
                                }}
                              />
                            )}
                        </Box>
                      }
                    />
                  </ListItemButton>
                </List>
              </Collapse>
            )
        )}
    </div>
  );
};

export default MenuListItemNestedComponent;
