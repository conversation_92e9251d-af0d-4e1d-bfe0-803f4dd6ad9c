import React, { useState, useEffect, useMemo } from "react";
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  Stack,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Badge,
  Avatar,
  CardHeader,
  Tabs,
  Tab,
  CircularProgress,
  Button,
  Link,
  Rating,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon,
  Visibility as VisibilityIcon,
  Business as BusinessIcon,
  LocationOn as LocationOnIcon,
  Star as StarIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Timeline as TimelineIcon,
  Assessment as AssessmentIcon,
  Language as LanguageIcon,
  Phone as PhoneIcon,
  Reviews as ReviewsIcon,
  Map as MapIcon,
  ExpandMore as ExpandMoreIcon,
  Launch as LaunchIcon,
  Speed as SpeedIcon,
  Analytics as AnalyticsIcon,
  GridOn as GridOnIcon,
  CompareArrows as CompareArrowsIcon,
} from "@mui/icons-material";
import {
  LocalFalconScanResult,
  LocalFalconRankingResult,
  LocalFalconTrendData,
  LocalFalconAlert,
  LocalFalconBusiness,
} from "../../services/localFalcon/localFalcon.service";

interface LocalFalconDashboardProps {
  scanResult: any | null; // Updated to handle the actual API response structure
  trendData: LocalFalconTrendData[];
  alerts: LocalFalconAlert[];
  competitors: LocalFalconBusiness[];
  loading: boolean;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`analytics-tabpanel-${index}`}
      aria-labelledby={`analytics-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

const LocalFalconDashboard: React.FC<LocalFalconDashboardProps> = ({
  scanResult,
  trendData,
  alerts,
  competitors,
  loading,
}) => {
  const [selectedMetric, setSelectedMetric] = useState<
    "position" | "visibility"
  >("position");
  const [activeTab, setActiveTab] = useState(0);

  // Utility function to safely format numbers
  const formatNumber = (
    value: any,
    decimals: number = 1,
    fallback: string = "N/A"
  ): string => {
    if (typeof value === "number" && !isNaN(value)) {
      return value.toFixed(decimals);
    }
    return fallback;
  };

  // Process the actual API response data
  const processedData = useMemo(() => {
    if (!scanResult?.rawResponse?.data) return null;

    const rawData = scanResult.rawResponse.data;
    const results = rawData.results || [];

    // Extract key metrics from Local Falcon response
    const totalPoints = rawData.points || 0;
    const foundPoints = rawData.found || 0;
    const visibilityPercent = rawData.percent || 0;
    const averageRankingPosition = rawData.arp || 0;
    const averageTopRankingPosition = rawData.atrp || 0;
    const shareOfLocalVoice = rawData.solv || 0;

    // Process grid results
    const gridResults = results.map((result: any, index: number) => ({
      ...result,
      gridIndex: index,
      gridRow: Math.floor(index / 5), // Assuming 5x5 grid
      gridCol: index % 5,
      hasResults: result.found,
      position: result.rank || null,
      totalResults: result.count || 0,
      businesses: result.results || [],
      targetBusiness: result.results?.find(
        (r: any) => r.place_id === scanResult.rawResponse.parameters.place_id
      ),
    }));

    // Analyze competitors
    const allBusinesses = new Map();
    results.forEach((result: any) => {
      if (result.results) {
        result.results.forEach((business: any) => {
          const key = business.place_id;
          if (!allBusinesses.has(key)) {
            allBusinesses.set(key, {
              ...business,
              appearances: 0,
              positions: [],
              averagePosition: 0,
              bestPosition: Infinity,
              worstPosition: 0,
            });
          }
          const existing = allBusinesses.get(key);
          existing.appearances++;
          existing.positions.push(business.rank);
          existing.bestPosition = Math.min(
            existing.bestPosition,
            business.rank
          );
          existing.worstPosition = Math.max(
            existing.worstPosition,
            business.rank
          );
          existing.averagePosition =
            existing.positions.reduce((a: number, b: number) => a + b, 0) /
            existing.positions.length;
        });
      }
    });

    const competitorAnalysis = Array.from(allBusinesses.values())
      .filter(
        (business: any) =>
          business.place_id !== scanResult.rawResponse.parameters.place_id
      )
      .sort((a: any, b: any) => b.appearances - a.appearances)
      .slice(0, 10);

    return {
      totalPoints,
      foundPoints,
      visibilityPercent,
      averageRankingPosition,
      averageTopRankingPosition,
      shareOfLocalVoice,
      gridResults,
      competitorAnalysis,
      targetBusiness: scanResult.businessName,
      keyword: scanResult.keyword,
    };
  }, [scanResult]);

  // Calculate trend indicators
  const getTrendIndicator = (current: number, previous: number) => {
    if (current < previous)
      return { icon: TrendingUpIcon, color: "success.main", text: "Improved" };
    if (current > previous)
      return { icon: TrendingDownIcon, color: "error.main", text: "Declined" };
    return { icon: TrendingFlatIcon, color: "text.secondary", text: "Stable" };
  };

  // Get visibility color based on percentage
  const getVisibilityColor = (percentage: number) => {
    if (percentage >= 80) return "success";
    if (percentage >= 60) return "warning";
    return "error";
  };

  // Get ranking color based on average position
  const getRankingColor = (position: number) => {
    if (position <= 3) return "success";
    if (position <= 10) return "warning";
    return "error";
  };

  const getBusinessInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word[0])
      .join("")
      .substring(0, 2)
      .toUpperCase();
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Calculate performance metrics
  const performanceMetrics = scanResult
    ? {
        averagePosition: scanResult.averagePosition,
        visibilityPercentage: scanResult.visibilityPercentage,
        totalSearches: scanResult.totalSearches,
        foundInResults: scanResult.foundInResults,
        topThreeCount: scanResult.rankings.filter((r) => r.position <= 3)
          .length,
        topTenCount: scanResult.rankings.filter((r) => r.position <= 10).length,
        notFoundCount: scanResult.rankings.filter((r) => r.position === 0)
          .length,
      }
    : null;

  // Get trend comparison if we have historical data
  const trendComparison =
    trendData.length >= 2
      ? {
          positionTrend: getTrendIndicator(
            scanResult?.averagePosition || 0,
            trendData[trendData.length - 2].averagePosition
          ),
          visibilityTrend: getTrendIndicator(
            trendData[trendData.length - 2].visibilityPercentage,
            scanResult?.visibilityPercentage || 0
          ),
        }
      : null;

  if (!processedData && !loading) {
    return (
      <Box sx={{ textAlign: "center", py: 4 }}>
        <AssessmentIcon sx={{ fontSize: 64, color: "text.secondary", mb: 2 }} />
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No Scan Results Available
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Run a Grid scan to see detailed analytics and insights
        </Typography>
      </Box>
    );
  }

  if (loading) {
    return (
      <Box sx={{ textAlign: "center", py: 4 }}>
        <CircularProgress size={60} sx={{ mb: 2 }} />
        <Typography variant="h6" color="text.secondary" gutterBottom>
          Processing Scan Results...
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Analyzing grid data and competitor rankings
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Card
        sx={{
          mb: 3,
          background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          color: "white",
        }}
      >
        <CardContent>
          <Stack direction="row" alignItems="center" spacing={2}>
            <Avatar
              sx={{ bgcolor: "rgba(255,255,255,0.2)", width: 56, height: 56 }}
            >
              <AnalyticsIcon sx={{ fontSize: 32 }} />
            </Avatar>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Geo Grid Analytics
              </Typography>
              <Typography variant="subtitle1" sx={{ opacity: 0.9 }}>
                {processedData?.keyword} • {processedData?.targetBusiness}
              </Typography>
            </Box>
            <Box sx={{ textAlign: "right" }}>
              <Typography variant="h3" fontWeight="bold">
                {typeof processedData?.visibilityPercent === "number"
                  ? processedData.visibilityPercent.toFixed(2)
                  : "0"}
                %
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.8 }}>
                Visibility Score
              </Typography>
            </Box>
          </Stack>
        </CardContent>
      </Card>

      {/* Tabs Navigation */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{
            "& .MuiTab-root": {
              minHeight: 64,
              fontSize: "1rem",
              fontWeight: 600,
            },
          }}
        >
          <Tab
            icon={<SpeedIcon />}
            label="Performance Overview"
            iconPosition="start"
            sx={{ textTransform: "none" }}
          />
          <Tab
            icon={<GridOnIcon />}
            label="Grid Analysis"
            iconPosition="start"
            sx={{ textTransform: "none" }}
          />
          <Tab
            icon={<CompareArrowsIcon />}
            label="Competitor Analysis"
            iconPosition="start"
            sx={{ textTransform: "none" }}
          />
          <Tab
            icon={<BusinessIcon />}
            label="Business Details"
            iconPosition="start"
            sx={{ textTransform: "none" }}
          />
        </Tabs>
      </Paper>

      {/* Tab Panels */}
      <TabPanel value={activeTab} index={0}>
        {/* Performance Overview Tab */}
        <Grid container spacing={3}>
          {/* Key Metrics Cards */}
          <Grid item xs={12}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Card
                  sx={{
                    height: "100%",
                    background:
                      "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
                    color: "white",
                  }}
                >
                  <CardContent sx={{ textAlign: "center" }}>
                    <SpeedIcon sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h3" fontWeight="bold">
                      {typeof processedData?.averageRankingPosition === "number"
                        ? processedData.averageRankingPosition.toFixed(1)
                        : "N/A"}
                    </Typography>
                    <Typography variant="body1" sx={{ opacity: 0.9 }}>
                      Average Position
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card
                  sx={{
                    height: "100%",
                    background:
                      "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
                    color: "white",
                  }}
                >
                  <CardContent sx={{ textAlign: "center" }}>
                    <VisibilityIcon sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h3" fontWeight="bold">
                      {typeof processedData?.visibilityPercent === "number"
                        ? processedData.visibilityPercent.toFixed(2)
                        : "0"}
                      %
                    </Typography>
                    <Typography variant="body1" sx={{ opacity: 0.9 }}>
                      Visibility
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card
                  sx={{
                    height: "100%",
                    background:
                      "linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)",
                    color: "white",
                  }}
                >
                  <CardContent sx={{ textAlign: "center" }}>
                    <GridOnIcon sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h3" fontWeight="bold">
                      {processedData?.foundPoints}/{processedData?.totalPoints}
                    </Typography>
                    <Typography variant="body1" sx={{ opacity: 0.9 }}>
                      Found in Grid
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} sm={6} md={3}>
                <Card
                  sx={{
                    height: "100%",
                    background:
                      "linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)",
                    color: "white",
                  }}
                >
                  <CardContent sx={{ textAlign: "center" }}>
                    <TrendingUpIcon sx={{ fontSize: 40, mb: 1 }} />
                    <Typography variant="h3" fontWeight="bold">
                      {processedData?.shareOfLocalVoice?.toFixed(2)}%
                    </Typography>
                    <Typography variant="body1" sx={{ opacity: 0.9 }}>
                      Share of Voice
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Grid>

          {/* Performance Charts */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: "100%" }}>
              <CardHeader
                avatar={<TrendingUpIcon color="primary" />}
                title="Ranking Distribution"
                titleTypographyProps={{ variant: "h6", fontWeight: 600 }}
              />
              <CardContent>
                <Stack spacing={3}>
                  {processedData?.gridResults?.filter(
                    (r: any) => r.hasResults && r.position <= 3
                  ).length > 0 && (
                    <Box>
                      <Stack
                        direction="row"
                        justifyContent="space-between"
                        alignItems="center"
                        sx={{ mb: 1 }}
                      >
                        <Typography variant="body2" fontWeight={500}>
                          Top 3 Positions
                        </Typography>
                        <Chip
                          label={
                            processedData?.gridResults?.filter(
                              (r: any) => r.hasResults && r.position <= 3
                            ).length || 0
                          }
                          color="success"
                          size="small"
                        />
                      </Stack>
                      <LinearProgress
                        variant="determinate"
                        value={
                          ((processedData?.gridResults?.filter(
                            (r: any) => r.hasResults && r.position <= 3
                          ).length || 0) /
                            (processedData?.totalPoints || 1)) *
                          100
                        }
                        color="success"
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                    </Box>
                  )}

                  {processedData?.gridResults?.filter(
                    (r: any) =>
                      r.hasResults && r.position > 3 && r.position <= 10
                  ).length > 0 && (
                    <Box>
                      <Stack
                        direction="row"
                        justifyContent="space-between"
                        alignItems="center"
                        sx={{ mb: 1 }}
                      >
                        <Typography variant="body2" fontWeight={500}>
                          Top 4-10 Positions
                        </Typography>
                        <Chip
                          label={
                            processedData?.gridResults?.filter(
                              (r: any) =>
                                r.hasResults &&
                                r.position > 3 &&
                                r.position <= 10
                            ).length || 0
                          }
                          color="warning"
                          size="small"
                        />
                      </Stack>
                      <LinearProgress
                        variant="determinate"
                        value={
                          ((processedData?.gridResults?.filter(
                            (r: any) =>
                              r.hasResults && r.position > 3 && r.position <= 10
                          ).length || 0) /
                            (processedData?.totalPoints || 1)) *
                          100
                        }
                        color="warning"
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                    </Box>
                  )}

                  <Box>
                    <Stack
                      direction="row"
                      justifyContent="space-between"
                      alignItems="center"
                      sx={{ mb: 1 }}
                    >
                      <Typography variant="body2" fontWeight={500}>
                        Not Found
                      </Typography>
                      <Chip
                        label={
                          (processedData?.totalPoints || 0) -
                          (processedData?.foundPoints || 0)
                        }
                        color="error"
                        size="small"
                      />
                    </Stack>
                    <LinearProgress
                      variant="determinate"
                      value={
                        (((processedData?.totalPoints || 0) -
                          (processedData?.foundPoints || 0)) /
                          (processedData?.totalPoints || 1)) *
                        100
                      }
                      color="error"
                      sx={{ height: 8, borderRadius: 4 }}
                    />
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          {/* Quick Stats */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: "100%" }}>
              <CardHeader
                avatar={<AssessmentIcon color="primary" />}
                title="Performance Insights"
                titleTypographyProps={{ variant: "h6", fontWeight: 600 }}
              />
              <CardContent>
                <Stack spacing={2}>
                  <Box
                    sx={{
                      p: 2,
                      bgcolor: "success.light",
                      borderRadius: 2,
                      color: "success.contrastText",
                    }}
                  >
                    <Typography variant="h6" fontWeight="bold">
                      Best Grid Position: #
                      {processedData?.gridResults
                        ?.filter((r: any) => r.hasResults)
                        .reduce(
                          (best: any, current: any) =>
                            !best || current.position < best.position
                              ? current
                              : best,
                          null
                        )?.position || "N/A"}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>
                      Highest ranking achieved in the grid
                    </Typography>
                  </Box>

                  <Box
                    sx={{
                      p: 2,
                      bgcolor: "info.light",
                      borderRadius: 2,
                      color: "info.contrastText",
                    }}
                  >
                    <Typography variant="h6" fontWeight="bold">
                      Average Top Ranking:{" "}
                      {processedData?.averageTopRankingPosition?.toFixed(1) ||
                        "N/A"}
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>
                      Average position when found in results
                    </Typography>
                  </Box>

                  <Box
                    sx={{
                      p: 2,
                      bgcolor: "warning.light",
                      borderRadius: 2,
                      color: "warning.contrastText",
                    }}
                  >
                    <Typography variant="h6" fontWeight="bold">
                      Grid Coverage:{" "}
                      {(
                        ((processedData?.foundPoints || 0) /
                          (processedData?.totalPoints || 1)) *
                        100
                      ).toFixed(1)}
                      %
                    </Typography>
                    <Typography variant="body2" sx={{ opacity: 0.8 }}>
                      Percentage of grid points where business appears
                    </Typography>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Grid Analysis Tab */}
      <TabPanel value={activeTab} index={1}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardHeader
                avatar={<GridOnIcon color="primary" />}
                title="Grid Performance Analysis"
                titleTypographyProps={{ variant: "h6", fontWeight: 600 }}
                subheader={`${processedData?.totalPoints} grid points analyzed`}
              />
              <CardContent>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>
                          <strong>Grid Position</strong>
                        </TableCell>
                        <TableCell>
                          <strong>Coordinates</strong>
                        </TableCell>
                        <TableCell>
                          <strong>Status</strong>
                        </TableCell>
                        <TableCell>
                          <strong>Ranking</strong>
                        </TableCell>
                        <TableCell>
                          <strong>Total Results</strong>
                        </TableCell>
                        <TableCell>
                          <strong>Actions</strong>
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {processedData?.gridResults
                        ?.slice(0, 15)
                        .map((result: any, index: number) => (
                          <TableRow key={index} hover>
                            <TableCell>
                              <Chip
                                label={`R${result.gridRow + 1}C${
                                  result.gridCol + 1
                                }`}
                                size="small"
                                variant="outlined"
                              />
                            </TableCell>
                            <TableCell>
                              <Typography
                                variant="body2"
                                color="text.secondary"
                              >
                                {typeof result.lat === "number"
                                  ? result.lat.toFixed(4)
                                  : "N/A"}
                                ,{" "}
                                {typeof result.lng === "number"
                                  ? result.lng.toFixed(4)
                                  : "N/A"}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={
                                  result.hasResults ? "Found" : "Not Found"
                                }
                                color={result.hasResults ? "success" : "error"}
                                size="small"
                                variant="outlined"
                              />
                            </TableCell>
                            <TableCell>
                              {result.hasResults ? (
                                <Chip
                                  label={`#${result.position}`}
                                  color={
                                    result.position <= 3
                                      ? "success"
                                      : result.position <= 10
                                      ? "warning"
                                      : "default"
                                  }
                                  size="small"
                                />
                              ) : (
                                <Typography
                                  variant="body2"
                                  color="text.secondary"
                                >
                                  -
                                </Typography>
                              )}
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2">
                                {result.totalResults || 0} results
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Tooltip title="View on Map">
                                <IconButton size="small" color="primary">
                                  <MapIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Competitor Analysis Tab */}
      <TabPanel value={activeTab} index={2}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardHeader
                avatar={<CompareArrowsIcon color="primary" />}
                title="Top Competitors"
                titleTypographyProps={{ variant: "h6", fontWeight: 600 }}
                subheader="Businesses appearing most frequently in search results"
              />
              <CardContent>
                <Grid container spacing={2}>
                  {processedData?.competitorAnalysis
                    ?.slice(0, 8)
                    .map((competitor: any, index: number) => (
                      <Grid
                        item
                        xs={12}
                        sm={6}
                        md={4}
                        lg={3}
                        key={competitor.place_id}
                      >
                        <Card variant="outlined" sx={{ height: "100%" }}>
                          <CardContent>
                            <Stack spacing={2}>
                              <Stack
                                direction="row"
                                alignItems="center"
                                spacing={2}
                              >
                                <Avatar sx={{ bgcolor: "primary.main" }}>
                                  {getBusinessInitials(competitor.business)}
                                </Avatar>
                                <Box sx={{ flex: 1, minWidth: 0 }}>
                                  <Typography
                                    variant="subtitle2"
                                    noWrap
                                    fontWeight={600}
                                  >
                                    {competitor.business}
                                  </Typography>
                                  <Stack
                                    direction="row"
                                    alignItems="center"
                                    spacing={1}
                                  >
                                    <Rating
                                      value={competitor.rating}
                                      size="small"
                                      readOnly
                                      precision={0.1}
                                    />
                                    <Typography
                                      variant="caption"
                                      color="text.secondary"
                                    >
                                      ({competitor.reviews})
                                    </Typography>
                                  </Stack>
                                </Box>
                              </Stack>

                              <Divider />

                              <Stack spacing={1}>
                                <Stack
                                  direction="row"
                                  justifyContent="space-between"
                                >
                                  <Typography
                                    variant="body2"
                                    color="text.secondary"
                                  >
                                    Appearances:
                                  </Typography>
                                  <Chip
                                    label={competitor.appearances}
                                    size="small"
                                    color="primary"
                                  />
                                </Stack>
                                <Stack
                                  direction="row"
                                  justifyContent="space-between"
                                >
                                  <Typography
                                    variant="body2"
                                    color="text.secondary"
                                  >
                                    Best Position:
                                  </Typography>
                                  <Chip
                                    label={`#${competitor.bestPosition}`}
                                    size="small"
                                    color={
                                      competitor.bestPosition <= 3
                                        ? "success"
                                        : "warning"
                                    }
                                  />
                                </Stack>
                                <Stack
                                  direction="row"
                                  justifyContent="space-between"
                                >
                                  <Typography
                                    variant="body2"
                                    color="text.secondary"
                                  >
                                    Avg Position:
                                  </Typography>
                                  <Typography variant="body2" fontWeight={500}>
                                    #{competitor.averagePosition.toFixed(1)}
                                  </Typography>
                                </Stack>
                              </Stack>

                              {competitor.url && (
                                <Button
                                  size="small"
                                  startIcon={<LaunchIcon />}
                                  href={competitor.url}
                                  target="_blank"
                                  variant="outlined"
                                  fullWidth
                                >
                                  Visit Website
                                </Button>
                              )}
                            </Stack>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Business Details Tab */}
      <TabPanel value={activeTab} index={3}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader
                avatar={<BusinessIcon color="primary" />}
                title="Target Business Details"
                titleTypographyProps={{ variant: "h6", fontWeight: 600 }}
              />
              <CardContent>
                <Stack spacing={3}>
                  <Box>
                    <Typography variant="h5" fontWeight="bold" gutterBottom>
                      {processedData?.targetBusiness}
                    </Typography>
                    <Typography
                      variant="body1"
                      color="text.secondary"
                      gutterBottom
                    >
                      Keyword: "{processedData?.keyword}"
                    </Typography>
                  </Box>

                  <Divider />

                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Box
                        sx={{
                          textAlign: "center",
                          p: 2,
                          bgcolor: "primary.light",
                          borderRadius: 2,
                        }}
                      >
                        <Typography
                          variant="h4"
                          fontWeight="bold"
                          color="primary.contrastText"
                        >
                          {processedData?.foundPoints}
                        </Typography>
                        <Typography
                          variant="body2"
                          color="primary.contrastText"
                        >
                          Grid Points Found
                        </Typography>
                      </Box>
                    </Grid>
                    <Grid item xs={6}>
                      <Box
                        sx={{
                          textAlign: "center",
                          p: 2,
                          bgcolor: "secondary.light",
                          borderRadius: 2,
                        }}
                      >
                        <Typography
                          variant="h4"
                          fontWeight="bold"
                          color="secondary.contrastText"
                        >
                          {processedData?.averageRankingPosition?.toFixed(1) ||
                            "N/A"}
                        </Typography>
                        <Typography
                          variant="body2"
                          color="secondary.contrastText"
                        >
                          Average Position
                        </Typography>
                      </Box>
                    </Grid>
                  </Grid>

                  <Box>
                    <Typography variant="h6" gutterBottom fontWeight={600}>
                      Performance Summary
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemIcon>
                          <VisibilityIcon color="primary" />
                        </ListItemIcon>
                        <ListItemText
                          primary="Visibility Score"
                          secondary={`${processedData?.visibilityPercent?.toFixed(
                            2
                          )}% - Business appears in ${
                            processedData?.foundPoints
                          } out of ${
                            processedData?.totalPoints
                          } grid locations`}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <TrendingUpIcon color="success" />
                        </ListItemIcon>
                        <ListItemText
                          primary="Share of Local Voice"
                          secondary={`${processedData?.shareOfLocalVoice?.toFixed(
                            2
                          )}% - Competitive presence in local search results`}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <SpeedIcon color="warning" />
                        </ListItemIcon>
                        <ListItemText
                          primary="Average Top Ranking Position"
                          secondary={`${
                            processedData?.averageTopRankingPosition?.toFixed(
                              1
                            ) || "N/A"
                          } - Average position when appearing in top results`}
                        />
                      </ListItem>
                    </List>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader
                avatar={<InfoIcon color="primary" />}
                title="Scan Configuration"
                titleTypographyProps={{ variant: "h6", fontWeight: 600 }}
              />
              <CardContent>
                <Stack spacing={2}>
                  <Box>
                    <Typography
                      variant="subtitle2"
                      color="text.secondary"
                      gutterBottom
                    >
                      Search Parameters
                    </Typography>
                    <Paper variant="outlined" sx={{ p: 2 }}>
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">
                            Keyword:
                          </Typography>
                          <Typography variant="body1" fontWeight={500}>
                            {scanResult?.keyword}
                          </Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">
                            Grid Size:
                          </Typography>
                          <Typography variant="body1" fontWeight={500}>
                            {scanResult?.gridConfiguration?.gridSize}
                          </Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">
                            Radius:
                          </Typography>
                          <Typography variant="body1" fontWeight={500}>
                            {scanResult?.gridConfiguration?.radius}{" "}
                            {scanResult?.gridConfiguration?.unit}
                          </Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">
                            Center Location:
                          </Typography>
                          <Typography variant="body1" fontWeight={500}>
                            {typeof scanResult?.gridConfiguration?.lat ===
                            "number"
                              ? scanResult.gridConfiguration.lat.toFixed(4)
                              : "N/A"}
                            ,{" "}
                            {typeof scanResult?.gridConfiguration?.lng ===
                            "number"
                              ? scanResult.gridConfiguration.lng.toFixed(4)
                              : "N/A"}
                          </Typography>
                        </Grid>
                      </Grid>
                    </Paper>
                  </Box>

                  <Box>
                    <Typography
                      variant="subtitle2"
                      color="text.secondary"
                      gutterBottom
                    >
                      Scan Results Summary
                    </Typography>
                    <Paper variant="outlined" sx={{ p: 2 }}>
                      <Stack spacing={1}>
                        <Stack direction="row" justifyContent="space-between">
                          <Typography variant="body2">
                            Total Grid Points:
                          </Typography>
                          <Typography variant="body2" fontWeight={500}>
                            {processedData?.totalPoints}
                          </Typography>
                        </Stack>
                        <Stack direction="row" justifyContent="space-between">
                          <Typography variant="body2">Points Found:</Typography>
                          <Typography variant="body2" fontWeight={500}>
                            {processedData?.foundPoints}
                          </Typography>
                        </Stack>
                        <Stack direction="row" justifyContent="space-between">
                          <Typography variant="body2">Coverage:</Typography>
                          <Typography variant="body2" fontWeight={500}>
                            {(
                              ((processedData?.foundPoints || 0) /
                                (processedData?.totalPoints || 1)) *
                              100
                            ).toFixed(1)}
                            %
                          </Typography>
                        </Stack>
                        <Stack direction="row" justifyContent="space-between">
                          <Typography variant="body2">
                            Competitors Found:
                          </Typography>
                          <Typography variant="body2" fontWeight={500}>
                            {processedData?.competitorAnalysis?.length || 0}
                          </Typography>
                        </Stack>
                      </Stack>
                    </Paper>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
    </Box>
  );
};

export default LocalFalconDashboard;
