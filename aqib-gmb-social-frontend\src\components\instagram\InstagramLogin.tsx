import React, { useContext } from "react";
import { <PERSON><PERSON> } from "@mui/material";
import { useDispatch } from "react-redux";
import InstagramService from "../../services/instagram/instagram.service";
import { ToastContext } from "../../context/toast.context";
import { ToastSeverity } from "../../constants/toastSeverity.constant";

interface InstagramLoginProps {
  userId: number;
  onLoginSuccess?: () => void;
}

const InstagramLogin: React.FC<InstagramLoginProps> = ({
  userId,
  onLoginSuccess,
}) => {
  const dispatch = useDispatch();
  const { setToastConfig } = useContext(ToastContext);
  const instagramService = new InstagramService(dispatch);

  const handleInstagramLogin = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation(); // Prevent parent tab click
    try {
      console.log("Instagram Login - userId:", userId);

      if (!userId) {
        setToastConfig(
          ToastSeverity.Error,
          "User ID is required for Instagram authentication",
          true
        );
        return;
      }

      const response = await instagramService.authenticate(userId);

      if (response?.success && response.authUrl) {
        // Redirect to Instagram OAuth
        window.location.href = response.authUrl;
      } else {
        setToastConfig(
          ToastSeverity.Error,
          "Failed to initiate Instagram authentication",
          true
        );
      }
    } catch (error: any) {
      console.error("Instagram authentication error:", error);
      setToastConfig(
        ToastSeverity.Error,
        "Error connecting to Instagram",
        true
      );
    }
  };

  return (
    <Button
      variant="outlined"
      onClick={handleInstagramLogin}
      sx={{
        borderColor: "#1976d2",
        color: "#1976d2",
        minHeight: "50px",
        minWidth: "120px",
        textTransform: "none",
        fontWeight: "bold",
        borderRadius: "4px",
        "&:hover": {
          borderColor: "#1565c0",
          backgroundColor: "rgba(25, 118, 210, 0.04)",
          color: "#1565c0",
        },
        "&:active": {
          borderColor: "#0d47a1",
          backgroundColor: "rgba(25, 118, 210, 0.08)",
          color: "#0d47a1",
        },
        "&:focus": {
          outline: "2px solid #1976d2",
          outlineOffset: "2px",
        },
      }}
    >
      <span className="responsiveHide">Connect</span>
    </Button>
  );
};

export default InstagramLogin;
